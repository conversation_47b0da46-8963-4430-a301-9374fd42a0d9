# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# Go executables
server/server
worker/worker

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
._*
Thumbs.db

# Logs
*.log
logs/
server/logs/
worker/logs/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
web/.next/
web/out/
web/build/
web/dist/

# Environment files
.env*
!.env.example

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Test data
test-data/

# Sensitive files
*secret*
*private*
*.pem
*.key

# Build artifacts
build/
dist/

# Database data
data/

# Compressed files
*.tar.gz
*.zip

# Coverage
coverage/
*.lcov 
config.local.yaml