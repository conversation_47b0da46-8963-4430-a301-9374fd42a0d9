version: '3.8'

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:6
    container_name: download-scheduler-mongodb
    restart: unless-stopped
    command: --wiredTigerCacheSizeGB 1
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: download_scheduler_dev
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - download-scheduler-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: download-scheduler-redis
    restart: unless-stopped
    command: redis-server --requirepass password123 --maxmemory 2gb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - download-scheduler-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3-management
    container_name: download-scheduler-rabbitmq
    restart: unless-stopped
    ports:
      - "5672:5672"   # AMQP 端口
      - "15672:15672" # 管理界面端口
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: password123
      RABBITMQ_VM_MEMORY_HIGH_WATERMARK: 0.6
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./scripts/rabbitmq-definitions.json:/etc/rabbitmq/definitions.json:ro
      - ./scripts/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    networks:
      - download-scheduler-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 可选：MongoDB Express 管理界面（仅开发环境）
  mongo-express:
    image: mongo-express:latest
    container_name: download-scheduler-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: password123
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - download-scheduler-network
    profiles:
      - tools

  # 可选：Redis Commander 管理界面（仅开发环境）
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: download-scheduler-redis-commander
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:0:password123
      HTTP_USER: admin
      HTTP_PASSWORD: password123
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - download-scheduler-network
    profiles:
      - tools

# 数据卷
volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local

# 网络
networks:
  download-scheduler-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
