import axios from 'axios'
import { getSession, signOut } from 'next-auth/react'

// 创建axios实例
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api',
  timeout: 10000,
})

// 401错误处理防抖标志
let isHandling401 = false

// 请求拦截器
api.interceptors.request.use(
  async (config) => {
    if (config.headers.Authorization) {
      // 如果已经存在Authorization头，则不添加
      return config
    }
    // 自动添加认证token
    try {
      const session = await getSession()
      if (session?.accessToken) {
        config.headers.Authorization = `Bearer ${session.accessToken}`
      }
    } catch (error) {
      console.error('Failed to get session:', error)
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    console.error('API Error:', error)
    
    // 如果是401错误，处理token过期的情况
    if (error.response?.status === 401) {
      // 检查当前页面，避免在登录页面处理401
      const isOnAuthPage = typeof window !== 'undefined' && 
        (window.location.pathname.includes('/auth/signin') || 
         window.location.pathname.includes('/auth/'))
      
      // 如果在登录页面，直接返回错误，不处理401
      if (isOnAuthPage) {
        return Promise.reject(error)
      }
      
      // 使用防抖机制，避免重复处理401错误
      if (!isHandling401) {
        isHandling401 = true
        
        console.log('处理401错误：会话已过期，正在清理并重定向到登录页面')
        
        try {
          // 使用NextAuth的signOut方法正确清理session
          await signOut({ 
            redirect: false, // 不自动跳转，我们手动控制
            callbackUrl: '/auth/signin' 
          })
          
          // 清理可能存在的本地缓存
          if (typeof window !== 'undefined') {
            localStorage.clear()
            sessionStorage.clear()
          }
          
          // 延迟跳转，给signOut时间完成
          setTimeout(() => {
            if (typeof window !== 'undefined') {
              // 保存当前页面作为回调URL
              const currentPath = window.location.pathname + window.location.search
              const callbackUrl = encodeURIComponent(currentPath)
              window.location.href = `/auth/signin?message=session_expired&callbackUrl=${callbackUrl}`
            }
          }, 100)
          
        } catch (signOutError) {
          console.error('Sign out error:', signOutError)
          // 如果signOut失败，直接跳转
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/signin?message=session_expired'
          }
        } finally {
          // 重置防抖标志（延迟重置，避免快速连续的401请求）
          setTimeout(() => {
            isHandling401 = false
          }, 2000)
        }
      }
      
      // 返回一个自定义的错误，避免上层组件继续处理
      return Promise.reject({
        ...error,
        handled: true, // 标记为已处理
        message: '会话已过期，正在重新登录...'
      })
    }
    
    // 处理其他HTTP错误
    if (error.response?.status >= 500) {
      console.error('服务器错误:', error.response.status, error.response.data)
    } else if (error.response?.status >= 400) {
      console.warn('客户端错误:', error.response.status, error.response.data)
    }
    
    return Promise.reject(error)
  }
)

// 用户认证相关API
export const authAPI = {
  // 获取当前用户信息
  getCurrentUser: () => api.get('/auth/me'),
  
  // 登出
  logout: () => api.post('/auth/logout'),
}

// 用户管理API
export const usersAPI = {
  // 获取用户列表
  getUsers: () => api.get('/users'),
  
  // 获取用户详情
  getUser: (id) => api.get(`/users/${id}`),
  
  // 创建用户
  createUser: (data) => api.post('/users', data),
  
  // 更新用户
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  
  // 删除用户
  deleteUser: (id) => api.delete(`/users/${id}`),
  
  // 修改密码
  changePassword: (id, data) => api.post(`/users/${id}/change-password`, data),
}

// 项目相关API
export const projectsAPI = {
  // 获取项目列表
  getProjects: () => api.get('/projects'),
  
  // 获取项目详情
  getProject: (id) => api.get(`/projects/${id}`),
  
  // 创建项目
  createProject: (data) => api.post('/projects', data),
  
  // 更新项目
  updateProject: (id, data) => api.put(`/projects/${id}`, data),
  
  // 删除项目
  deleteProject: (id) => api.delete(`/projects/${id}`),
  
  // 暂停项目
  pauseProject: (id) => api.post(`/projects/${id}/pause`),
  
  // 恢复项目
  resumeProject: (id) => api.post(`/projects/${id}/resume`),
  
  // 获取项目统计
  getProjectStats: (id) => api.get(`/tasks/stats/${id}`),
  
  // 获取项目监控数据
  getProjectMonitor: (id) => api.get(`/monitor/project/${id}`),
}

// Worker相关API
export const workersAPI = {
  // 获取Worker列表
  getWorkers: () => api.get('/workers'),
  
  // 获取Worker详情
  getWorker: (id) => api.get(`/workers/${id}`),
  
  // Worker注册
  registerWorker: (data) => api.post('/workers/register', data),
  
  // 更新Worker
  updateWorker: (id, data) => api.put(`/workers/${id}`, data),
  
  // 禁用Worker
  disableWorker: (id) => api.post(`/workers/${id}/disable`),
  
  // 启用Worker
  enableWorker: (id) => api.post(`/workers/${id}/enable`),
  
  // 删除Worker
  deleteWorker: (id) => api.delete(`/workers/${id}`),
}

// 任务相关API
export const tasksAPI = {
  // 批量提交任务 (使用项目token)
  submitBatch: (data, projectToken) => api.post('/tasks/batch', data, {
    headers: {
      'Authorization': projectToken
    }
  }),
  
  // 获取任务统计
  getTaskStats: (projectId) => api.get(`/tasks/stats/${projectId}`),
}

// 监控相关API
export const monitorAPI = {
  // 获取项目监控数据
  getProjectMonitor: (id) => api.get(`/monitor/project/${id}`),
  
  // 获取系统状态
  getSystemStatus: () => api.get('/monitor/system'),
}

// 通用API方法
export const apiUtils = {
  // 处理API错误
  handleError: (error) => {
    // 如果是已处理的401错误，返回友好提示
    if (error.handled && error.message) {
      return {
        message: error.message,
        status: 401,
        handled: true
      }
    }
    
    if (error.response) {
      // 服务器返回错误状态码
      const message = error.response.data?.error || 
                     error.response.data?.message || 
                     this.getDefaultErrorMessage(error.response.status)
      
      return {
        message,
        status: error.response.status,
        data: error.response.data
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      return {
        message: '网络连接错误，请检查网络连接后重试',
        status: 0
      }
    } else {
      // 其他错误
      return {
        message: error.message || '未知错误',
        status: -1
      }
    }
  },
  
  // 获取默认错误消息
  getDefaultErrorMessage: (status) => {
    const errorMessages = {
      400: '请求参数错误',
      401: '认证失败，请重新登录',
      403: '权限不足',
      404: '资源不存在',
      409: '数据冲突',
      422: '数据验证失败',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂不可用',
      504: '请求超时'
    }
    
    return errorMessages[status] || `服务器错误 (${status})`
  },
  
  // 检查用户权限
  hasPermission: (userRole, requiredRole) => {
    const roleHierarchy = {
      'viewer': 0,
      'operator': 1,
      'admin': 2
    }
    
    const userLevel = roleHierarchy[userRole] || 0
    const requiredLevel = roleHierarchy[requiredRole] || 0
    
    return userLevel >= requiredLevel
  },
  
  // 格式化文件大小
  formatFileSize: (bytes) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },
  
  // 格式化时间
  formatDuration: (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟${secs}秒`
    } else {
      return `${secs}秒`
    }
  },
  
  // 格式化成功率
  formatSuccessRate: (completed, total) => {
    if (total === 0) return '0%'
    return ((completed / total) * 100).toFixed(1) + '%'
  }
}

export default api 