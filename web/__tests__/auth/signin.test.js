import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { signIn } from 'next-auth/react'
import { useRouter } from 'next/router'
import SignIn from '../../pages/auth/signin'

// Mock next-auth
jest.mock('next-auth/react')
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}))

// Mock toast hook
const mockToast = jest.fn()
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}))

describe('SignIn Page', () => {
  const mockPush = jest.fn()

  beforeEach(() => {
    useRouter.mockReturnValue({
      push: mockPush,
      query: {},
    })
    signIn.mockClear()
    mockPush.mockClear()
    mockToast.mockClear()
  })

  it('renders signin form correctly', () => {
    render(<SignIn />)
    
    expect(screen.getByText('分布式下载调度系统')).toBeInTheDocument()
    expect(screen.getByText('请登录到管理控制台')).toBeInTheDocument()
    expect(screen.getByLabelText('用户名')).toBeInTheDocument()
    expect(screen.getByLabelText('密码')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
  })

  it('handles successful login', async () => {
    signIn.mockResolvedValue({ ok: true })
    render(<SignIn />)
    
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    const submitButton = screen.getByRole('button', { name: '登录' })

    fireEvent.change(usernameInput, { target: { value: 'admin' } })
    fireEvent.change(passwordInput, { target: { value: 'admin123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(signIn).toHaveBeenCalledWith('credentials', {
        username: 'admin',
        password: 'admin123',
        redirect: false,
      })
    })
  })

  it('handles login failure', async () => {
    signIn.mockResolvedValue({ ok: false })
    render(<SignIn />)
    
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    const submitButton = screen.getByRole('button', { name: '登录' })

    fireEvent.change(usernameInput, { target: { value: 'wrong' } })
    fireEvent.change(passwordInput, { target: { value: 'credentials' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "登录失败",
        description: "用户名或密码错误",
        variant: "destructive",
      })
    })
  })
})