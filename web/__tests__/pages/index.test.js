import { render, screen } from '@testing-library/react'
import Dashboard from '../../pages/index'

// Mock SWR
jest.mock('swr', () => {
  return jest.fn(() => ({
    data: null,
    error: null,
    isLoading: false
  }))
})

// Mock API services
jest.mock('../../services/api', () => ({
  projectsAPI: {
    getProjects: jest.fn(),
  },
  workersAPI: {
    getWorkers: jest.fn(),
  },
  apiUtils: {
    handleError: jest.fn(),
  },
}))

// Mock toast hook
const mockToast = jest.fn()
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}))

describe('Dashboard Page', () => {
  beforeEach(() => {
    mockToast.mockClear()
  })

  it('renders dashboard title and description', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('控制台')).toBeInTheDocument()
    expect(screen.getByText('分布式下载调度系统概览')).toBeInTheDocument()
  })

  it('renders statistics cards', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('总项目数')).toBeInTheDocument()
    expect(screen.getByText('Worker节点')).toBeInTheDocument()
    expect(screen.getByText('系统状态')).toBeInTheDocument()
    expect(screen.getByText('今日任务')).toBeInTheDocument()
  })

  it('renders project and worker sections', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('最近项目')).toBeInTheDocument()
    expect(screen.getByText('最新创建的项目列表')).toBeInTheDocument()
    expect(screen.getByText('Worker状态')).toBeInTheDocument()
    expect(screen.getByText('当前活跃的Worker节点')).toBeInTheDocument()
  })

  it('renders quick actions section', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('快速操作')).toBeInTheDocument()
    expect(screen.getByText('常用功能快捷入口')).toBeInTheDocument()
    expect(screen.getByText('创建新项目')).toBeInTheDocument()
    expect(screen.getByText('管理Worker')).toBeInTheDocument()
    expect(screen.getByText('查看监控')).toBeInTheDocument()
  })

  it('shows loading state when data is loading', () => {
    const useSWR = require('swr')
    useSWR.mockReturnValue({
      data: null,
      error: null,
      isLoading: true
    })

    render(<Dashboard />)
    
    expect(screen.getAllByText('加载中...').length).toBeGreaterThan(0)
  })

  it('shows no data message when no projects exist', () => {
    const useSWR = require('swr')
    useSWR.mockReturnValue({
      data: { projects: [] },
      error: null,
      isLoading: false
    })

    render(<Dashboard />)
    
    expect(screen.getByText('暂无项目')).toBeInTheDocument()
  })

  it('displays system status as normal', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('正常')).toBeInTheDocument()
    expect(screen.getByText('所有服务运行正常')).toBeInTheDocument()
  })
}) 