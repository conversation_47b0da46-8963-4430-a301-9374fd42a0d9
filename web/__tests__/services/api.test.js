import { apiUtils } from '../../services/api'

describe('API Utils', () => {
  describe('handleError', () => {
    it('should handle response error', () => {
      const error = {
        response: {
          status: 404,
          data: { message: 'Not found' }
        }
      }

      const result = apiUtils.handleError(error)
      
      expect(result).toEqual({
        message: 'Not found',
        status: 404,
        data: { message: 'Not found' }
      })
    })

    it('should handle network error', () => {
      const error = {
        request: {}
      }

      const result = apiUtils.handleError(error)
      
      expect(result).toEqual({
        message: '网络连接错误',
        status: 0
      })
    })
  })

  describe('formatting functions', () => {
    it('should format file size correctly', () => {
      expect(apiUtils.formatFileSize(0)).toBe('0 B')
      expect(apiUtils.formatFileSize(1024)).toBe('1 KB')
      expect(apiUtils.formatFileSize(1048576)).toBe('1 MB')
      expect(apiUtils.formatFileSize(1073741824)).toBe('1 GB')
    })

    it('should format duration correctly', () => {
      expect(apiUtils.formatDuration(30)).toBe('30秒')
      expect(apiUtils.formatDuration(90)).toBe('1分钟30秒')
      expect(apiUtils.formatDuration(3661)).toBe('1小时1分钟')
    })

    it('should format success rate correctly', () => {
      expect(apiUtils.formatSuccessRate(0, 0)).toBe('0%')
      expect(apiUtils.formatSuccessRate(50, 100)).toBe('50.0%')
      expect(apiUtils.formatSuccessRate(333, 1000)).toBe('33.3%')
    })
  })
})

 