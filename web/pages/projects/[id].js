import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/router'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetT<PERSON>le, <PERSON><PERSON><PERSON>rigger, SheetFooter } from '@/components/ui/sheet'
import { FormInput } from '@/components/ui/form-input'
import { CopyableToken } from '@/components/ui/copyable-token'
import { useToast } from '@/hooks/use-toast'
import { projectsAPI, tasksAPI, apiUtils } from '@/services/api'
import { 
  ArrowLeft, 
  RefreshCw, 
  Play, 
  Pause, 
  Upload,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Activity,
  Plus,
  FileText,
  Trash2,
  Settings
} from 'lucide-react'

export default function ProjectDetail() {
  const router = useRouter()
  const { id } = router.query
  const { toast } = useToast()
  
  // 状态管理
  const [project, setProject] = useState(null)
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [statsLoading, setStatsLoading] = useState(false)
  const [actionLoading, setActionLoading] = useState(false)
  const [showSubmitSheet, setShowSubmitSheet] = useState(false)
  const [countdown, setCountdown] = useState(5) // 添加倒计时状态
  
  // 任务提交表单
  const [taskForm, setTaskForm] = useState({
    requests: [{ url: '', method: 'GET', headers: {} }],
    priority: 5
  })
  
  // 临时header输入状态
  const [headerInputs, setHeaderInputs] = useState([{ key: '', value: '' }])
  
  // 自动刷新定时器
  const intervalRef = useRef(null)
  const countdownRef = useRef(null) // 添加倒计时定时器引用

  // 添加请求项
  const addRequest = () => {
    setTaskForm(prev => ({
      ...prev,
      requests: [...prev.requests, { url: '', method: 'GET', headers: {} }]
    }))
    setHeaderInputs(prev => [...prev, { key: '', value: '' }])
  }

  // 删除请求项
  const removeRequest = (index) => {
    if (taskForm.requests.length <= 1) return
    setTaskForm(prev => ({
      ...prev,
      requests: prev.requests.filter((_, i) => i !== index)
    }))
    setHeaderInputs(prev => prev.filter((_, i) => i !== index))
  }

  // 更新请求项
  const updateRequest = (index, field, value) => {
    setTaskForm(prev => ({
      ...prev,
      requests: prev.requests.map((req, i) => 
        i === index ? { ...req, [field]: value } : req
      )
    }))
  }

  // 更新Header输入
  const updateHeaderInput = (requestIndex, field, value) => {
    setHeaderInputs(prev => prev.map((input, i) => 
      i === requestIndex ? { ...input, [field]: value } : input
    ))
  }

  // 添加Header
  const addHeader = (requestIndex) => {
    const { key, value } = headerInputs[requestIndex]
    if (!key || !value) {
      toast({
        title: "添加失败",
        description: "请输入Header名称和值",
        variant: "destructive"
      })
      return
    }
    
    setTaskForm(prev => ({
      ...prev,
      requests: prev.requests.map((req, i) => 
        i === requestIndex ? { 
          ...req, 
          headers: { ...req.headers, [key]: value }
        } : req
      )
    }))
    
    // 清空输入框
    setHeaderInputs(prev => prev.map((input, i) => 
      i === requestIndex ? { key: '', value: '' } : input
    ))
    
    toast({
      title: "Header已添加",
      description: `已添加 ${key}: ${value}`
    })
  }

  // 删除Header
  const removeHeader = (requestIndex, headerKey) => {
    setTaskForm(prev => ({
      ...prev,
      requests: prev.requests.map((req, i) => 
        i === requestIndex ? { 
          ...req, 
          headers: Object.fromEntries(
            Object.entries(req.headers).filter(([key]) => key !== headerKey)
          )
        } : req
      )
    }))
  }

  // 加载项目信息
  const loadProject = async () => {
    if (!id) return
    
    try {
      setLoading(true)
      const response = await projectsAPI.getProject(id)
      setProject(response.data)
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      toast({
        title: "加载失败",
        description: errorInfo.message,
        variant: "destructive"
      })
      if (errorInfo.status === 404) {
        router.push('/projects')
      }
    } finally {
      setLoading(false)
    }
  }

  // 加载统计数据
  const loadStats = async () => {
    if (!id) return
    
    try {
      setStatsLoading(true)
      const response = await tasksAPI.getTaskStats(id)
      setStats(response.data)
      // 重置倒计时
      setCountdown(5)
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      console.error('Failed to load stats:', errorInfo)
    } finally {
      setStatsLoading(false)
    }
  }

  // 暂停/恢复项目
  const handleToggleProject = async () => {
    if (!project) return
    
    try {
      setActionLoading(true)
      if (project.status === 'active') {
        await projectsAPI.pauseProject(project.id)
        toast({
          title: "项目已暂停",
          description: `项目 "${project.name}" 已暂停`
        })
      } else {
        await projectsAPI.resumeProject(project.id)
        toast({
          title: "项目已恢复",
          description: `项目 "${project.name}" 已恢复`
        })
      }
      loadProject()
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      toast({
        title: "操作失败",
        description: errorInfo.message,
        variant: "destructive"
      })
    } finally {
      setActionLoading(false)
    }
  }

  // 提交任务
  const handleSubmitTasks = async () => {
    const validRequests = taskForm.requests.filter(req => req.url.trim())
    
    if (!project || validRequests.length === 0) {
      toast({
        title: "提交失败",
        description: "请至少输入一个有效的下载URL",
        variant: "destructive"
      })
      return
    }

    try {
      setActionLoading(true)
      console.log("project.token", project.token)
      
      const response = await tasksAPI.submitBatch({
        projectId: project.id,
        requests: validRequests,
        priority: taskForm.priority
      }, project.token)
      
      toast({
        title: "任务提交成功",
        description: `已提交 ${validRequests.length} 个下载任务`
      })
      
      setShowSubmitSheet(false)
      setTaskForm({ requests: [{ url: '', method: 'GET', headers: {} }], priority: 5 })
      setHeaderInputs([{ key: '', value: '' }])
      loadStats()
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      toast({
        title: "提交失败",
        description: errorInfo.message,
        variant: "destructive"
      })
    } finally {
      setActionLoading(false)
    }
  }

  // 获取状态徽章
  const getStatusBadge = (status) => {
    const config = {
      active: { label: '运行中', variant: 'default' },
      paused: { label: '已暂停', variant: 'secondary' },
      stopped: { label: '已停止', variant: 'destructive' }
    }
    const { label, variant } = config[status] || { label: status, variant: 'outline' }
    return <Badge variant={variant}>{label}</Badge>
  }

  // 启动倒计时
  const startCountdown = () => {
    setCountdown(5)
    countdownRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          return 5 // 重置为5秒
        }
        return prev - 1
      })
    }, 1000)
  }

  // 初始化和清理
  useEffect(() => {
    if (id) {
      loadProject()
      loadStats()
      
      // 每5秒刷新一次统计数据
      intervalRef.current = setInterval(loadStats, 5000)
      // 启动倒计时
      startCountdown()
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      if (countdownRef.current) {
        clearInterval(countdownRef.current)
      }
    }
  }, [id])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold text-foreground mb-2">项目不存在</h3>
        <p className="text-muted-foreground mb-4">指定的项目未找到</p>
        <Button onClick={() => router.push('/projects')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回项目列表
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.push('/projects')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回
          </Button>
          <div>
            <div className="flex items-center space-x-3">
              <h1 className="text-3xl font-bold text-foreground">{project.name}</h1>
              {getStatusBadge(project.status)}
            </div>
            <p className="text-muted-foreground mt-2">
              创建时间: {new Date(project.createdAt).toLocaleString()}
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button onClick={loadStats} variant="outline" size="sm" disabled={statsLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${statsLoading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
          <Sheet open={showSubmitSheet} onOpenChange={setShowSubmitSheet}>
            <SheetTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                提交任务
              </Button>
            </SheetTrigger>
            <SheetContent className="min-w-[500px] sm:min-w-[600px] overflow-y-auto max-h-screen">
              <SheetHeader>
                <SheetTitle>批量提交下载任务</SheetTitle>
              </SheetHeader>
              <div className="py-6 space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">下载请求列表</label>
                    <Button size="sm" variant="outline" onClick={addRequest}>
                      <Plus className="w-4 h-4 mr-1" />
                      添加请求
                    </Button>
                  </div>
                  
                  {taskForm.requests.map((request, index) => (
                    <Card key={index} className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">请求 #{index + 1}</span>
                          {taskForm.requests.length > 1 && (
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              onClick={() => removeRequest(index)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-xs text-muted-foreground">URL</label>
                          <input
                            type="text"
                            className="w-full p-2 border rounded-md text-sm"
                            placeholder="https://example.com/file.zip"
                            value={request.url}
                            onChange={(e) => updateRequest(index, 'url', e.target.value)}
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-xs text-muted-foreground">HTTP方法</label>
                          <select
                            className="w-full p-2 border rounded-md text-sm"
                            value={request.method}
                            onChange={(e) => updateRequest(index, 'method', e.target.value)}
                          >
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="HEAD">HEAD</option>
                          </select>
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-xs text-muted-foreground">Headers</label>
                          <div className="space-y-2">
                            {Object.entries(request.headers).map(([key, value]) => (
                              <div key={key} className="flex space-x-2">
                                <input
                                  type="text"
                                  className="flex-1 p-2 border rounded-md text-sm"
                                  value={key}
                                  disabled
                                />
                                <input
                                  type="text"
                                  className="flex-1 p-2 border rounded-md text-sm"
                                  value={value}
                                  disabled
                                />
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => removeHeader(index, key)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            ))}
                            <div className="flex space-x-2">
                              <input
                                type="text"
                                className="flex-1 p-2 border rounded-md text-sm"
                                placeholder="Header名称"
                                value={headerInputs[index]?.key || ''}
                                onChange={(e) => updateHeaderInput(index, 'key', e.target.value)}
                              />
                              <input
                                type="text"
                                className="flex-1 p-2 border rounded-md text-sm"
                                placeholder="Header值"
                                value={headerInputs[index]?.value || ''}
                                onChange={(e) => updateHeaderInput(index, 'value', e.target.value)}
                              />
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => addHeader(index)}
                              >
                                <Plus className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
                
                {/* <div className="space-y-2">
                  <label className="text-sm font-medium">优先级</label>
                  <select
                    className="w-full p-2 border rounded-md"
                    value={taskForm.priority}
                    onChange={(e) => setTaskForm({ ...taskForm, priority: parseInt(e.target.value) })}
                  >
                    <option value={1}>1 - 最低</option>
                    <option value={3}>3 - 低</option>
                    <option value={5}>5 - 中等</option>
                    <option value={7}>7 - 高</option>
                    <option value={10}>10 - 最高</option>
                  </select>
                </div> */}
              </div>
              <SheetFooter>
                <Button variant="outline" onClick={() => setShowSubmitSheet(false)}>
                  取消
                </Button>
                <Button onClick={handleSubmitTasks} disabled={actionLoading}>
                  {actionLoading ? '提交中...' : '提交任务'}
                </Button>
              </SheetFooter>
            </SheetContent>
          </Sheet>
          <Button
            variant={project.status === 'active' ? 'outline' : 'default'}
            onClick={handleToggleProject}
            disabled={actionLoading}
          >
            {project.status === 'active' ? (
              <>
                <Pause className="w-4 h-4 mr-2" />
                暂停项目
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                恢复项目
              </>
            )}
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总任务数</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTasks?.toLocaleString() || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">等待中</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {stats.pendingTasks?.toLocaleString() || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">下载中</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.downloadingTasks?.toLocaleString() || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已完成</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats.completedTasks?.toLocaleString() || 0}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 详细信息 */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* 项目配置 */}
        <Card>
          <CardHeader>
            <CardTitle>项目配置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">打包大小</label>
                <p className="text-lg">{project.config.packSizeGB} GB</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">并发数</label>
                <p className="text-lg">{project.config.concurrent}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">重试次数</label>
                <p className="text-lg">{project.config.retryTimes} 次</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">下载超时</label>
                <p className="text-lg">{project.config.downloadTimeout} 秒</p>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <label className="text-sm font-medium text-muted-foreground">OSS配置</label>
              <div className="mt-2 space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>服务商:</span>
                  <span className="font-medium">{project.config.ossConfig.provider}</span>
                </div>
                <div className="flex justify-between">
                  <span>存储桶:</span>
                  <span className="font-medium">{project.config.ossConfig.bucket}</span>
                </div>
                <div className="flex justify-between">
                  <span>区域:</span>
                  <span className="font-medium">{project.config.ossConfig.region}</span>
                </div>
                <div className="flex justify-between">
                  <span>前缀:</span>
                  <span className="font-medium">{project.config.ossConfig.prefix || '/'}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 任务统计详情 */}
        {stats && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                任务统计详情
                <span className="text-xs text-muted-foreground font-normal">
                  {countdown}s后自动刷新
                </span>
              </CardTitle>
              <CardDescription>
                最后更新: {stats.lastUpdate ? new Date(stats.lastUpdate * 1000).toLocaleString() : '暂无数据'} 
                <span className="text-xs ml-2">(每5秒自动刷新)</span>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <XCircle className="w-4 h-4 mr-2 text-red-500" />
                    失败任务
                  </div>
                  <span className="font-medium text-red-600">
                    {stats.failedTasks?.toLocaleString() || 0}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Download className="w-4 h-4 mr-2 text-blue-500" />
                    下载速度
                  </div>
                  <span className="font-medium">
                    {apiUtils.formatFileSize(stats.downloadSpeed || 0)}/s
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Upload className="w-4 h-4 mr-2 text-green-500" />
                    总大小
                  </div>
                  <span className="font-medium">
                    {apiUtils.formatFileSize(stats.totalSize || 0)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    成功率
                  </div>
                  <span className="font-medium text-green-600">
                    {stats.successRate?.toFixed(1) || 0}%
                  </span>
                </div>
              </div>

              {/* 进度条 */}
              {stats.totalTasks > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>完成进度</span>
                    <span>{((stats.completedTasks / stats.totalTasks) * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300" 
                      style={{ 
                        width: `${(stats.completedTasks / stats.totalTasks) * 100}%` 
                      }}
                    ></div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Token */}
      <Card>
        <CardHeader>
          <CardTitle>API Token</CardTitle>
          <CardDescription>
            使用此Token来调用API提交任务
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CopyableToken 
            token={project.token} 
            label="项目Token"
            hideToken={true}
          />
        </CardContent>
      </Card>
    </div>
  )
} 