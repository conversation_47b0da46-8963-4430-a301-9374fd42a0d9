import { useState, useEffect } from 'react'
import useS<PERSON> from 'swr'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { projectsAPI, workersAPI, apiUtils } from '@/services/api'
import { useToast } from '@/hooks/use-toast'
import { 
  Download, 
  Users, 
  FolderOpen, 
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp
} from 'lucide-react'

// SWR fetcher函数
const fetcher = async (url) => {
  if (url === 'projects') {
    const response = await projectsAPI.getProjects()
    return response.data
  } else if (url === 'workers') {
    const response = await workersAPI.getWorkers()
    return response.data
  }
  throw new Error('Unknown endpoint')
}

export default function Dashboard() {
  const { toast } = useToast()
  
  // 使用SWR获取数据
  const { data: projects, error: projectsError, isLoading: projectsLoading } = useSWR('projects', fetcher, {
    refreshInterval: 5000, // 每5秒刷新一次
    revalidateOnFocus: false
  })
  
  const { data: workers, error: workersError, isLoading: workersLoading } = useSWR('workers', fetcher, {
    refreshInterval: 5000,
    revalidateOnFocus: false
  })

  // 计算统计数据
  const stats = {
    totalProjects: projects?.projects?.length || 0,
    activeProjects: projects?.projects?.filter(p => p.status === 'active')?.length || 0,
    totalWorkers: workers?.workers?.length || 0,
    activeWorkers: workers?.workers?.filter(w => w.status === 'active')?.length || 0,
  }

  // 处理错误
  useEffect(() => {
    if (projectsError) {
      const error = apiUtils.handleError(projectsError)
      toast({
        title: "获取项目数据失败",
        description: error.message,
        variant: "destructive",
      })
    }
    if (workersError) {
      const error = apiUtils.handleError(workersError)
      toast({
        title: "获取Worker数据失败", 
        description: error.message,
        variant: "destructive",
      })
    }
  }, [projectsError, workersError, toast])

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">控制台</h1>
        <p className="text-muted-foreground">
          分布式下载调度系统概览
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总项目数</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProjects}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeProjects} 个活跃项目
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Worker节点</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalWorkers}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeWorkers} 个在线
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统状态</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">正常</div>
            <p className="text-xs text-muted-foreground">
              所有服务运行正常
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日任务</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">--</div>
            <p className="text-xs text-muted-foreground">
              暂无统计数据
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息 */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* 最近项目 */}
        <Card>
          <CardHeader>
            <CardTitle>最近项目</CardTitle>
            <CardDescription>
              最新创建的项目列表
            </CardDescription>
          </CardHeader>
          <CardContent>
            {projectsLoading ? (
              <div className="text-sm text-muted-foreground">加载中...</div>
            ) : projects?.projects?.length > 0 ? (
              <div className="space-y-3">
                {projects.projects.slice(0, 5).map((project) => (
                  <div key={project.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{project.name}</p>
                      <p className="text-xs text-muted-foreground">
                        创建于 {new Date(project.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge variant={project.status === 'active' ? 'default' : 'secondary'}>
                      {project.status === 'active' ? '运行中' : '已暂停'}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">暂无项目</div>
            )}
          </CardContent>
        </Card>

        {/* Worker状态 */}
        <Card>
          <CardHeader>
            <CardTitle>Worker状态</CardTitle>
            <CardDescription>
              当前活跃的Worker节点
            </CardDescription>
          </CardHeader>
          <CardContent>
            {workersLoading ? (
              <div className="text-sm text-muted-foreground">加载中...</div>
            ) : workers?.workers?.length > 0 ? (
              <div className="space-y-3">
                {workers.workers.slice(0, 5).map((worker) => (
                  <div key={worker.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{worker.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {worker.host}:{worker.port}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {worker.status === 'active' ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <Badge variant={worker.status === 'active' ? 'default' : 'destructive'}>
                        {worker.status === 'active' ? '在线' : '离线'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">暂无Worker</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
          <CardDescription>
            常用功能快捷入口
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-3">
            <Button className="h-20 flex-col" variant="outline">
              <FolderOpen className="h-6 w-6 mb-2" />
              创建新项目
            </Button>
            <Button className="h-20 flex-col" variant="outline">
              <Users className="h-6 w-6 mb-2" />
              管理Worker
            </Button>
            <Button className="h-20 flex-col" variant="outline">
              <TrendingUp className="h-6 w-6 mb-2" />
              查看监控
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
