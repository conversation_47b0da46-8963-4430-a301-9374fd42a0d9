import { useState, useEffect } from 'react';
import WorkerCard from '../components/worker-card';
import WorkerDetailSheet from '../components/worker-detail-sheet';
import WorkerRegisterForm from '../components/worker-register-form';
import { RefreshCw, Plus, Users, Activity, AlertTriangle, Clock } from 'lucide-react';
import { useToast } from '../hooks/use-toast';
import { workersAPI, apiUtils } from '../services/api';

export default function Workers() {
  const [workers, setWorkers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [filter, setFilter] = useState('all');
  const [selectedWorker, setSelectedWorker] = useState(null);
  const [showRegisterForm, setShowRegisterForm] = useState(false);
  const { toast } = useToast();

  // 获取 workers 列表
  const fetchWorkers = async () => {
    try {
      const response = await workersAPI.getWorkers();
      setWorkers(response.data.workers || []);
    } catch (error) {
      console.error('Error fetching workers:', error);
      const errorInfo = apiUtils.handleError(error);
      
      // 如果是已处理的401错误，不显示错误消息（因为会自动跳转到登录页面）
      if (!errorInfo.handled) {
        toast({
          title: "获取 Workers 失败",
          description: errorInfo.message,
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchWorkers();
  }, []);

  // 自动刷新
  useEffect(() => {
    let interval;
    if (autoRefresh) {
      interval = setInterval(fetchWorkers, 10000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  // 手动刷新
  const handleRefresh = () => {
    setLoading(true);
    fetchWorkers();
  };

  // 注册成功回调
  const handleRegisterSuccess = (result) => {
    fetchWorkers(); // 刷新列表
    toast({
      title: "Worker 预注册成功",
      description: `Worker ${result.id} 已创建，请使用提供的Token启动Worker服务`,
    });
  };

  // 过滤 workers
  const filteredWorkers = workers.filter(worker => {
    switch (filter) {
      case 'active':
        return worker.status === 'active';
      case 'offline':
        return worker.status === 'offline';
      case 'pending':
        return worker.status === 'pending';
      case 'error':
        return worker.status === 'error';
      default:
        return true;
    }
  });

  // 统计数据
  const stats = {
    total: workers.length,
    active: workers.filter(w => w.status === 'active').length,
    offline: workers.filter(w => w.status === 'offline').length,
    pending: workers.filter(w => w.status === 'pending').length,
    error: workers.filter(w => w.status === 'error').length,
    activeTasks: workers.reduce((sum, w) => sum + (w.stats?.activeTasks || 0), 0)
  };

  // 获取状态显示文本
  const getStatusText = (status) => {
    const statusMap = {
      pending: '等待激活',
      active: '正常工作',
      offline: '离线',
      error: '错误',
      disabled: '已禁用'
    };
    return statusMap[status] || status;
  };

  return (
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Worker 管理</h1>
            <p className="text-gray-600">管理和监控分布式下载 Worker 节点</p>
          </div>
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span>自动刷新 (10秒)</span>
            </label>
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>刷新</span>
            </button>
            <button
              onClick={() => setShowRegisterForm(true)}
              className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              <Plus className="w-4 h-4" />
              <span>预注册 Worker</span>
            </button>
          </div>
        </div>

        {/* 统计面板 */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-gray-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总 Workers</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <Activity className="w-8 h-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">正常工作</p>
                <p className="text-2xl font-bold text-green-600">{stats.active}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">等待激活</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <AlertTriangle className="w-8 h-8 text-gray-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">离线</p>
                <p className="text-2xl font-bold text-gray-500">{stats.offline}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <AlertTriangle className="w-8 h-8 text-red-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">错误状态</p>
                <p className="text-2xl font-bold text-red-600">{stats.error}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <Activity className="w-8 h-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">活跃任务</p>
                <p className="text-2xl font-bold text-blue-600">{stats.activeTasks}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 过滤器 */}
        <div className="flex space-x-2">
          {[
            { key: 'all', label: '全部', count: stats.total },
            { key: 'active', label: '正常工作', count: stats.active },
            { key: 'pending', label: '等待激活', count: stats.pending },
            { key: 'offline', label: '离线', count: stats.offline },
            { key: 'error', label: '错误', count: stats.error },
          ].map(({ key, label, count }) => (
            <button
              key={key}
              onClick={() => setFilter(key)}
              className={`px-3 py-2 text-sm rounded-md ${
                filter === key
                  ? 'bg-blue-100 text-blue-700 border-blue-300'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              } border`}
            >
              {label} ({count})
            </button>
          ))}
        </div>

        {/* Workers 列表 */}
        {loading && workers.length === 0 ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">加载 Workers 中...</p>
            </div>
          </div>
        ) : filteredWorkers.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filter === 'all' ? '暂无 Worker 节点' : `暂无${
                {
                  active: '正常工作',
                  pending: '等待激活',
                  offline: '离线',
                  error: '错误状态'
                }[filter]
              }的 Worker`}
            </h3>
            <p className="text-gray-500 mb-4">
              {filter === 'all' 
                ? '点击"预注册 Worker"按钮来添加新的 Worker 节点'
                : '请选择其他过滤条件查看 Worker 节点'
              }
            </p>
            {filter === 'all' && (
              <button
                onClick={() => setShowRegisterForm(true)}
                className="inline-flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                <Plus className="w-4 h-4" />
                <span>预注册 Worker</span>
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredWorkers.map((worker) => (
              <WorkerCard
                key={worker.id}
                worker={worker}
                onClick={() => setSelectedWorker(worker)}
              />
            ))}
          </div>
        )}

        {/* Worker 详情 */}
        {selectedWorker && (
          <WorkerDetailSheet
            worker={selectedWorker}
            isOpen={!!selectedWorker}
            onClose={() => setSelectedWorker(null)}
          />
        )}

        {/* Worker 注册表单 */}
        <WorkerRegisterForm
          isOpen={showRegisterForm}
          onClose={() => setShowRegisterForm(false)}
          onSuccess={handleRegisterSuccess}
        />
      </div>
  );
}
