import '@/styles/globals.css'
import { SessionProvider } from 'next-auth/react'
import Layout from '../components/layout'
import { Toaster } from '@/components/ui/toaster'

function MyApp({ Component, pageProps: { session, ...pageProps } }) {
  return (
    <SessionProvider session={session}>
      <Layout>
        <Component {...pageProps} />
        <Toaster />
      </Layout>
    </SessionProvider>
  )
}

export default MyApp
