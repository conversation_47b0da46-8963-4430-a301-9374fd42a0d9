import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import UserCard from '../components/user-card';
import UserCreateForm from '../components/user-create-form';
import UserEditForm from '../components/user-edit-form';
import UserDetailSheet from '../components/user-detail-sheet';
import UserPasswordForm from '../components/user-password-form';
import { RefreshCw, Plus, Users, Shield, Eye, Settings } from 'lucide-react';
import { useToast } from '../hooks/use-toast';
import { usersAPI, apiUtils } from '../services/api';

export default function UsersPage() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDetailSheet, setShowDetailSheet] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  // 权限检查
  useEffect(() => {
    if (status === 'loading') return; // 等待session加载
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    
    // 只有管理员可以访问用户管理页面
    if (session.user?.role !== 'admin') {
      toast({
        title: "权限不足",
        description: "只有管理员可以访问用户管理页面",
        variant: "destructive",
      });
      router.push('/');
      return;
    }
  }, [session, status, router]);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await usersAPI.getUsers();
      setUsers(response.data.users || []);
    } catch (error) {
      const errorMsg = apiUtils.handleError(error);
      console.error('Error fetching users:', error);
      
      // 如果是已处理的401错误，不显示错误消息（因为会自动跳转到登录页面）
      if (!errorMsg.handled) {
        toast({
          title: "获取用户列表失败",
          description: errorMsg.message,
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    if (session?.user?.role === 'admin') {
      fetchUsers();
    }
  }, [session]);

  // 手动刷新
  const handleRefresh = () => {
    fetchUsers();
  };

  // 创建用户成功回调
  const handleCreateSuccess = () => {
    setShowCreateForm(false);
    fetchUsers();
    toast({
      title: "创建用户成功",
      description: "新用户已成功创建",
    });
  };

  // 编辑用户成功回调
  const handleEditSuccess = () => {
    setShowEditForm(false);
    setSelectedUser(null);
    fetchUsers();
    toast({
      title: "更新用户成功",
      description: "用户信息已成功更新",
    });
  };

  // 密码修改成功回调
  const handlePasswordChangeSuccess = () => {
    setShowPasswordForm(false);
    setSelectedUser(null);
    toast({
      title: "密码修改成功",
      description: "用户密码已成功更新",
    });
  };

  // 删除用户
  const handleDeleteUser = async (userId) => {
    if (!confirm('确定要删除这个用户吗？此操作不可恢复。')) {
      return;
    }

    try {
      await usersAPI.deleteUser(userId);
      fetchUsers();
      toast({
        title: "删除用户成功",
        description: "用户已成功删除",
      });
    } catch (error) {
      const errorMsg = apiUtils.handleError(error);
      // 如果是已处理的401错误，不显示错误消息
      if (!errorMsg.handled) {
        toast({
          title: "删除用户失败",
          description: errorMsg.message,
          variant: "destructive",
        });
      }
    }
  };

  // 切换用户状态
  const handleToggleUserStatus = async (userId, currentStatus) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      await usersAPI.updateUser(userId, { status: newStatus });
      fetchUsers();
      toast({
        title: "用户状态已更新",
        description: `用户已${newStatus === 'active' ? '激活' : '禁用'}`,
      });
    } catch (error) {
      const errorMsg = apiUtils.handleError(error);
      // 如果是已处理的401错误，不显示错误消息
      if (!errorMsg.handled) {
        toast({
          title: "更新用户状态失败",
          description: errorMsg.message,
          variant: "destructive",
        });
      }
    }
  };

  // 打开编辑表单
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowEditForm(true);
  };

  // 打开密码修改表单
  const handleChangePassword = (user) => {
    setSelectedUser(user);
    setShowPasswordForm(true);
  };

  // 查看用户详情
  const handleViewUser = (user) => {
    setSelectedUser(user);
    setShowDetailSheet(true);
  };

  // 过滤用户
  const filteredUsers = users.filter(user => {
    switch (filter) {
      case 'admin':
        return user.role === 'admin';
      case 'operator':
        return user.role === 'operator';
      case 'viewer':
        return user.role === 'viewer';
      case 'active':
        return user.status === 'active';
      case 'inactive':
        return user.status === 'inactive';
      default:
        return true;
    }
  });

  // 统计数据
  const stats = {
    total: users.length,
    admin: users.filter(u => u.role === 'admin').length,
    operator: users.filter(u => u.role === 'operator').length,
    viewer: users.filter(u => u.role === 'viewer').length,
    active: users.filter(u => u.status === 'active').length,
    inactive: users.filter(u => u.status === 'inactive').length,
  };

  // 如果不是管理员，不渲染页面内容
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!session || session.user?.role !== 'admin') {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
          <p className="text-gray-600">管理系统用户账户和权限</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>刷新</span>
          </button>
          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
          >
            <Plus className="w-4 h-4" />
            <span>创建用户</span>
          </button>
        </div>
      </div>

      {/* 统计面板 */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-gray-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总用户</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <Shield className="w-8 h-8 text-red-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">管理员</p>
              <p className="text-2xl font-bold text-red-600">{stats.admin}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <Settings className="w-8 h-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">操作员</p>
              <p className="text-2xl font-bold text-blue-600">{stats.operator}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <Eye className="w-8 h-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">查看者</p>
              <p className="text-2xl font-bold text-green-600">{stats.viewer}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">活跃用户</p>
              <p className="text-2xl font-bold text-green-600">{stats.active}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-gray-400" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">非活跃</p>
              <p className="text-2xl font-bold text-gray-500">{stats.inactive}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 筛选器 */}
      <div className="bg-white p-4 rounded-lg shadow border">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 text-sm rounded-full ${
              filter === 'all' 
                ? 'bg-blue-100 text-blue-800 border border-blue-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            全部 ({stats.total})
          </button>
          <button
            onClick={() => setFilter('admin')}
            className={`px-3 py-1 text-sm rounded-full ${
              filter === 'admin' 
                ? 'bg-red-100 text-red-800 border border-red-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            管理员 ({stats.admin})
          </button>
          <button
            onClick={() => setFilter('operator')}
            className={`px-3 py-1 text-sm rounded-full ${
              filter === 'operator' 
                ? 'bg-blue-100 text-blue-800 border border-blue-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            操作员 ({stats.operator})
          </button>
          <button
            onClick={() => setFilter('viewer')}
            className={`px-3 py-1 text-sm rounded-full ${
              filter === 'viewer' 
                ? 'bg-green-100 text-green-800 border border-green-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            查看者 ({stats.viewer})
          </button>
          <button
            onClick={() => setFilter('active')}
            className={`px-3 py-1 text-sm rounded-full ${
              filter === 'active' 
                ? 'bg-green-100 text-green-800 border border-green-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            活跃 ({stats.active})
          </button>
          <button
            onClick={() => setFilter('inactive')}
            className={`px-3 py-1 text-sm rounded-full ${
              filter === 'inactive' 
                ? 'bg-gray-100 text-gray-800 border border-gray-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            非活跃 ({stats.inactive})
          </button>
        </div>
      </div>

      {/* 用户列表 */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : filteredUsers.length === 0 ? (
        <div className="text-center py-12">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无用户</h3>
          <p className="text-gray-500">点击"创建用户"按钮添加第一个用户</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUsers.map(user => (
            <UserCard
              key={user.id}
              user={user}
              currentUserId={session.user?.id}
              onView={() => handleViewUser(user)}
              onEdit={() => handleEditUser(user)}
              onDelete={() => handleDeleteUser(user.id)}
              onToggleStatus={() => handleToggleUserStatus(user.id, user.status)}
              onChangePassword={() => handleChangePassword(user)}
            />
          ))}
        </div>
      )}

      {/* 创建用户表单 */}
      {showCreateForm && (
        <UserCreateForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setShowCreateForm(false)}
        />
      )}

      {/* 编辑用户表单 */}
      {showEditForm && selectedUser && (
        <UserEditForm
          user={selectedUser}
          onSuccess={handleEditSuccess}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedUser(null);
          }}
        />
      )}

      {/* 密码修改表单 */}
      {showPasswordForm && selectedUser && (
        <UserPasswordForm
          user={selectedUser}
          onSuccess={handlePasswordChangeSuccess}
          onCancel={() => {
            setShowPasswordForm(false);
            setSelectedUser(null);
          }}
        />
      )}

      {/* 用户详情 */}
      {showDetailSheet && selectedUser && (
        <UserDetailSheet
          user={selectedUser}
          onClose={() => {
            setShowDetailSheet(false);
            setSelectedUser(null);
          }}
          onChangePassword={() => {
            setShowDetailSheet(false);
            handleChangePassword(selectedUser);
          }}
        />
      )}
    </div>
  );
} 