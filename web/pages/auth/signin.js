import { useState, useEffect } from 'react'
import { signIn, getSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import { Lock, User, AlertTriangle } from 'lucide-react'

export default function SignIn() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  // 检查URL参数并显示相应提示
  useEffect(() => {
    const { message } = router.query
    
    if (message === 'session_expired') {
      toast({
        title: "会话已过期",
        description: "您的登录会话已过期，请重新登录",
        variant: "destructive",
      })
    }
  }, [router.query, toast])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const result = await signIn('credentials', {
        username,
        password,
        redirect: false,
      })

      if (result?.ok) {
        toast({
          title: "登录成功",
          description: "正在跳转到控制台...",
        })
        
        // 获取callbackUrl或默认跳转到首页
        const callbackUrl = router.query.callbackUrl || '/'
        router.push(callbackUrl)
      } else {
        toast({
          title: "登录失败",
          description: "用户名或密码错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Login error:', error)
      toast({
        title: "登录失败",
        description: "系统错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 检查是否有会话过期消息
  const hasSessionExpiredMessage = router.query.message === 'session_expired'

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            分布式下载调度系统
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            请登录到管理控制台
          </p>
        </div>
        
        {/* 会话过期提示 */}
        {hasSessionExpiredMessage && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              <div className="text-sm text-red-800">
                <strong>会话已过期</strong>
                <p className="mt-1">您的登录会话已过期，请重新登录以继续使用系统</p>
              </div>
            </div>
          </div>
        )}
        
        <Card>
          <CardHeader>
            <CardTitle className="text-center">登录</CardTitle>
            <CardDescription className="text-center">
              使用您的账户凭据登录系统
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">用户名</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="username"
                    type="text"
                    placeholder="请输入用户名"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="pl-10"
                    required
                    disabled={isLoading}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="请输入密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10"
                    required
                    disabled={isLoading}
                  />
                </div>
              </div>
              
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading || !username || !password}
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </form>
            
            <div className="mt-4 text-sm text-gray-600 space-y-1">
              <p><strong>测试账户:</strong></p>
              <p>管理员: admin / admin123</p>
              <p>操作员: operator / operator123</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 如果用户已经登录，重定向到首页
export async function getServerSideProps(context) {
  const session = await getSession(context)
  
  if (session) {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    }
  }
  
  return {
    props: {},
  }
} 