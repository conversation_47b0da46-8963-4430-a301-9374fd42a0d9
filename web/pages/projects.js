import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Sheet, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from '@/components/ui/sheet'
import { useToast } from '@/hooks/use-toast'
import { projectsAPI, apiUtils } from '@/services/api'
import { ProjectCard } from '@/components/project-card'
import { ProjectConfigForm } from '@/components/project-config-form'
import { FormInput } from '@/components/ui/form-input'
import { 
  Plus, 
  RefreshCw,
  Download
} from 'lucide-react'

export default function Projects() {
  const router = useRouter()
  const [projects, setProjects] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedProject, setSelectedProject] = useState(null)
  const [showCreateSheet, setShowCreateSheet] = useState(false)
  const [showEditSheet, setShowEditSheet] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [actionLoading, setActionLoading] = useState(false)
  const { toast } = useToast()

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    config: {
      packSizeGB: 10,
      ossConfig: {
        provider: 'aliyun',
        endpoint: '',
        bucket: '',
        accessKey: '',
        secretKey: '',
        prefix: '',
        region: '',
        forcePathStyle: false
      },
      concurrent: 5,
      retryTimes: 3,
      downloadTimeout: 300
    }
  })

  // 加载项目列表
  const loadProjects = async () => {
    try {
      setLoading(true)
      const response = await projectsAPI.getProjects()
      setProjects(response.data.projects || [])
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      toast({
        title: "加载失败",
        description: errorInfo.message,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadProjects()
  }, [])

  // 创建项目
  const handleCreateProject = async () => {
    try {
      setActionLoading(true)
      const response = await projectsAPI.createProject(formData)
      toast({
        title: "创建成功",
        description: `项目 "${formData.name}" 已创建`
      })
      setShowCreateSheet(false)
      resetForm()
      loadProjects()
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      toast({
        title: "创建失败",
        description: errorInfo.message,
        variant: "destructive"
      })
    } finally {
      setActionLoading(false)
    }
  }

  // 更新项目
  const handleUpdateProject = async () => {
    try {
      setActionLoading(true)
      await projectsAPI.updateProject(selectedProject.id, formData)
      toast({
        title: "更新成功",
        description: `项目 "${formData.name}" 已更新`
      })
      setShowEditSheet(false)
      resetForm()
      loadProjects()
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      toast({
        title: "更新失败",
        description: errorInfo.message,
        variant: "destructive"
      })
    } finally {
      setActionLoading(false)
    }
  }

  // 删除项目
  const handleDeleteProject = async () => {
    try {
      setActionLoading(true)
      await projectsAPI.deleteProject(selectedProject.id)
      toast({
        title: "删除成功",
        description: `项目 "${selectedProject.name}" 已删除`
      })
      setShowDeleteDialog(false)
      setSelectedProject(null)
      loadProjects()
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      toast({
        title: "删除失败",
        description: errorInfo.message,
        variant: "destructive"
      })
    } finally {
      setActionLoading(false)
    }
  }

  // 暂停/恢复项目
  const handleToggleProject = async (project) => {
    try {
      setActionLoading(true)
      if (project.status === 'active') {
        await projectsAPI.pauseProject(project.id)
        toast({
          title: "项目已暂停",
          description: `项目 "${project.name}" 已暂停`
        })
      } else {
        await projectsAPI.resumeProject(project.id)
        toast({
          title: "项目已恢复",
          description: `项目 "${project.name}" 已恢复`
        })
      }
      loadProjects()
    } catch (error) {
      const errorInfo = apiUtils.handleError(error)
      toast({
        title: "操作失败",
        description: errorInfo.message,
        variant: "destructive"
      })
    } finally {
      setActionLoading(false)
    }
  }

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      config: {
        packSizeGB: 10,
        ossConfig: {
          provider: 'aliyun',
          endpoint: '',
          bucket: '',
          accessKey: '',
          secretKey: '',
          prefix: '',
          region: '',
          forcePathStyle: false
        },
        concurrent: 5,
        retryTimes: 3,
        downloadTimeout: 300
      }
    })
  }

  // 编辑项目
  const handleEditClick = (project) => {
    setSelectedProject(project)
    setFormData({
      name: project.name,
      config: project.config
    })
    setShowEditSheet(true)
  }

  // 删除确认
  const handleDeleteClick = (project) => {
    setSelectedProject(project)
    setShowDeleteDialog(true)
  }

  // 查看详情
  const handleDetailClick = (project) => {
    router.push(`/projects/${project.id}`)
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">项目管理</h1>
          <p className="text-muted-foreground mt-2">
            管理下载项目，配置OSS存储和下载参数
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={loadProjects} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <Sheet open={showCreateSheet} onOpenChange={setShowCreateSheet}>
            <SheetTrigger asChild>
              <Button onClick={() => { resetForm(); setShowCreateSheet(true) }}>
                <Plus className="w-4 h-4 mr-2" />
                创建项目
              </Button>
            </SheetTrigger>
            <SheetContent className="w-[600px] sm:w-[600px] overflow-y-auto">
              <SheetHeader>
                <SheetTitle>创建新项目</SheetTitle>
              </SheetHeader>
              <div className="py-6">
                <ProjectConfigForm 
                  formData={formData} 
                  setFormData={setFormData}
                  mode="create"
                />
              </div>
              <SheetFooter className="mt-6">
                <Button variant="outline" onClick={() => setShowCreateSheet(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateProject} disabled={actionLoading}>
                  {actionLoading ? '创建中...' : '创建项目'}
                </Button>
              </SheetFooter>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* 项目列表 */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {projects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              onToggle={handleToggleProject}
              onEdit={handleEditClick}
              onDelete={handleDeleteClick}
              onDetail={handleDetailClick}
              actionLoading={actionLoading}
            />
          ))}
        </div>
      )}

      {/* 空状态 */}
      {!loading && projects.length === 0 && (
        <div className="text-center py-12">
          <Download className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">还没有项目</h3>
          <p className="text-muted-foreground mb-4">创建您的第一个下载项目来开始使用系统</p>
          <Button onClick={() => setShowCreateSheet(true)}>
            <Plus className="w-4 h-4 mr-2" />
            创建项目
          </Button>
        </div>
      )}

      {/* 编辑项目Sheet */}
      <Sheet open={showEditSheet} onOpenChange={setShowEditSheet}>
        <SheetContent className="w-[600px] sm:w-[600px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>编辑项目</SheetTitle>
          </SheetHeader>
          <div className="py-6">
            <ProjectConfigForm 
              formData={formData} 
              setFormData={setFormData}
              mode="edit"
            />
          </div>
          <SheetFooter className="mt-6">
            <Button variant="outline" onClick={() => setShowEditSheet(false)}>
              取消
            </Button>
            <Button onClick={handleUpdateProject} disabled={actionLoading}>
              {actionLoading ? '更新中...' : '更新项目'}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="bg-white shadow-lg">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>您确定要删除项目 <strong>"{selectedProject?.name}"</strong> 吗？</p>
            <p className="text-sm text-muted-foreground mt-2">
              此操作不可逆，将删除项目的所有相关数据。
            </p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteProject} 
              disabled={actionLoading}
            >
              {actionLoading ? '删除中...' : '确认删除'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
