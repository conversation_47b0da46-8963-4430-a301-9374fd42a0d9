import NextAuth from 'next-auth'
import Credentials<PERSON>rovider from 'next-auth/providers/credentials'

// 获取后端API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        try {
          // 调用后端登录API
          const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              username: credentials?.username,
              password: credentials?.password,
            }),
          })

          if (!response.ok) {
            console.error('Login failed:', response.status, response.statusText)
            return null
          }

          const data = await response.json()
          
          if (data.token && data.user) {
            return {
              id: data.user.id,
              name: data.user.name,
              username: data.user.username,
              role: data.user.role,
              email: data.user.email,
              accessToken: data.token,
              tokenExpires: data.expiresAt,
            }
          }

          return null
        } catch (error) {
          console.error('Login error:', error)
          return null
        }
      }
    })
  ],
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  callbacks: {
    async jwt({ token, user }) {
      // 用户首次登录时，将用户信息和token添加到JWT
      if (user) {
        token.accessToken = user.accessToken
        token.role = user.role
        token.username = user.username
        token.tokenExpires = user.tokenExpires
      }
      
      // 检查token是否过期
      if (token.tokenExpires && Date.now() > token.tokenExpires * 1000) {
        // Token已过期，返回错误
        return { error: 'TokenExpired' }
      }

      return token
    },
    async session({ session, token }) {
      // 如果token有错误，传递给session
      if (token.error) {
        session.error = token.error
      }
      
      if (token.accessToken) {
        session.accessToken = token.accessToken
        session.user.id = token.sub
        session.user.role = token.role
        session.user.username = token.username
        session.tokenExpires = token.tokenExpires
      }
      
      return session
    },
    async signIn({ user, account, profile }) {
      // 如果登录成功，返回true
      if (user) {
        return true
      }
      return false
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET || 'development-secret-key',
  debug: process.env.NODE_ENV === 'development',
}

export default NextAuth(authOptions) 