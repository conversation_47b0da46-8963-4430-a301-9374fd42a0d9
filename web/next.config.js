/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    return [
      // 排除 auth 相关的 API 路径，这些由 NextAuth 处理
      {
        source: '/api/workers/:path*',
        destination: 'http://localhost:8080/api/workers/:path*',
      },
      {
        source: '/api/projects/:path*',
        destination: 'http://localhost:8080/api/projects/:path*',
      },
      {
        source: '/api/tasks/:path*',
        destination: 'http://localhost:8080/api/tasks/:path*',
      },
      {
        source: '/api/monitor/:path*',
        destination: 'http://localhost:8080/api/monitor/:path*',
      },
      {
        source: '/ping',
        destination: 'http://localhost:8080/ping',
      },
    ]
  },
};

module.exports = nextConfig;
