# 分布式下载调度系统 - Web前端

基于Next.js + shadcn-ui构建的现代化管理界面。

## 功能特性

- ✅ **用户认证**: 基于NextAuth.js的安全登录系统
- ✅ **响应式设计**: 支持桌面端和移动端的优美界面
- ✅ **实时监控**: 项目和Worker状态的实时更新
- ✅ **现代化UI**: 使用shadcn-ui组件库和Tailwind CSS
- ✅ **完整测试**: Jest + React Testing Library测试覆盖

## 技术栈

- **框架**: Next.js 14
- **UI库**: shadcn-ui + Radix UI
- **样式**: Tailwind CSS
- **认证**: NextAuth.js
- **状态管理**: SWR (用于数据获取)
- **表单**: React Hook Form + Zod
- **测试**: Jest + React Testing Library
- **图标**: Lucide React

## 快速开始

### 1. 安装依赖

\`\`\`bash
npm install
\`\`\`

### 2. 配置环境变量

复制环境变量示例文件：

\`\`\`bash
cp env.example .env.local
\`\`\`

编辑 \`.env.local\` 文件：

\`\`\`env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key
NEXT_PUBLIC_API_URL=http://localhost:8080/api
\`\`\`

### 3. 启动开发服务器

\`\`\`bash
npm run dev
\`\`\`

访问 [http://localhost:3000](http://localhost:3000)

## 测试

### 运行测试

\`\`\`bash
# 运行所有测试
npm test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
\`\`\`

### 测试覆盖率

当前测试覆盖了以下模块：
- 登录页面 (认证流程)
- Dashboard页面 (主控制台)
- UI组件 (Button, Card等)
- API工具函数

## 页面结构

```
pages/
├── _app.js              # 应用入口，包含认证和Toast
├── index.js             # 控制台页面
├── projects.js          # 项目管理页面 (待开发)
├── workers.js           # Worker管理页面 (待开发)
├── auth/
│   └── signin.js        # 登录页面
└── api/
    └── auth/
        └── [...nextauth].js  # NextAuth配置
```

## 组件结构

```
components/
├── layout.js            # 主布局组件
└── ui/                  # shadcn-ui基础组件
    ├── button.js
    ├── card.js
    ├── input.js
    ├── label.js
    ├── dialog.js
    ├── toast.js
    ├── toaster.js
    └── badge.js
```

## 登录凭据

系统提供以下测试账户：

- **管理员**: admin / admin123
- **操作员**: operator / operator123

## API集成

前端通过以下API与后端通信：

- **项目管理**: \`/api/projects\`
- **Worker管理**: \`/api/workers\`
- **任务管理**: \`/api/tasks\`
- **实时监控**: \`/api/monitor\`

## 开发指南

### 添加新页面

1. 在 \`pages/\` 目录下创建新的页面文件
2. 在 \`components/layout.js\` 中添加导航链接
3. 添加相应的测试文件

### 添加新组件

1. 在 \`components/ui/\` 下创建新组件
2. 遵循shadcn-ui的设计模式
3. 编写相应的测试

### API调用

使用 \`services/api.js\` 中的API函数：

\`\`\`javascript
import { projectsAPI } from '@/services/api'
import useSWR from 'swr'

function MyComponent() {
  const { data, error } = useSWR('projects', () => projectsAPI.getProjects())
  // ...
}
\`\`\`

## 构建部署

### 构建生产版本

\`\`\`bash
npm run build
\`\`\`

### 启动生产服务器

\`\`\`bash
npm run start
\`\`\`

### Docker部署

项目根目录包含Dockerfile，支持容器化部署：

\`\`\`bash
docker build -t download-scheduler-web .
docker run -p 3000:3000 download-scheduler-web
\`\`\`

## 贡献指南

1. 确保所有测试通过：\`npm test\`
2. 保持代码覆盖率在80%以上
3. 遵循现有的代码风格和组件结构
4. 为新功能添加相应的测试 