import { X, User, Mail, Calendar, Shield, CheckCircle, XCircle, Clock, UserCircle, Key } from 'lucide-react';

export default function UserDetailSheet({ user, onClose, onChangePassword }) {
  // 获取角色配置
  const getRoleConfig = (role) => {
    const configs = {
      admin: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: Shield,
        text: '管理员',
        description: '拥有系统的完全管理权限，可以管理用户、项目、Worker等所有功能'
      },
      operator: {
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: UserCircle,
        text: '操作员',
        description: '可以操作项目和Worker，但不能管理用户账户'
      },
      viewer: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: User,
        text: '查看者',
        description: '只能查看系统数据，不能进行任何修改操作'
      }
    };
    return configs[role] || configs.viewer;
  };

  // 获取状态配置
  const getStatusConfig = (status) => {
    const configs = {
      active: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle,
        text: '活跃',
        description: '用户可以正常登录和使用系统'
      },
      inactive: {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: XCircle,
        text: '非活跃',
        description: '用户暂时无法登录系统'
      },
      disabled: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: XCircle,
        text: '已禁用',
        description: '用户账户已被禁用，无法访问系统'
      }
    };
    return configs[status] || configs.inactive;
  };

  const roleConfig = getRoleConfig(user.role);
  const statusConfig = getStatusConfig(user.status);
  const RoleIcon = roleConfig.icon;
  const StatusIcon = statusConfig.icon;

  // 格式化时间显示
  const formatDateTime = (dateString) => {
    if (!dateString) return '从未';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 计算账户存在时间
  const getAccountAge = (createdAt) => {
    if (!createdAt) return '未知';
    const created = new Date(createdAt);
    const now = new Date();
    const diffInDays = Math.floor((now - created) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return '今天创建';
    if (diffInDays === 1) return '1天前创建';
    if (diffInDays < 30) return `${diffInDays}天前创建`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)}个月前创建`;
    return `${Math.floor(diffInDays / 365)}年前创建`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">用户详情</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* 用户基本信息 */}
          <div className="flex items-start space-x-6 mb-8">
            {/* 头像 */}
            <div className="flex-shrink-0">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-2xl">
                  {user.name?.charAt(0)?.toUpperCase() || user.username?.charAt(0)?.toUpperCase()}
                </span>
              </div>
            </div>
            
            {/* 基本信息 */}
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{user.name}</h3>
              <p className="text-lg text-gray-600 mb-3">@{user.username}</p>
              
              {/* 角色和状态标签 */}
              <div className="flex items-center space-x-3 mb-4">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${roleConfig.color}`}>
                  <RoleIcon className="w-4 h-4 mr-2" />
                  {roleConfig.text}
                </div>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig.color}`}>
                  <StatusIcon className="w-4 h-4 mr-2" />
                  {statusConfig.text}
                </div>
              </div>
              
              {user.email && (
                <div className="flex items-center text-gray-600">
                  <Mail className="w-4 h-4 mr-2" />
                  <span>{user.email}</span>
                </div>
              )}
            </div>
          </div>

          {/* 详细信息卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 角色权限信息 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                <Shield className="w-4 h-4 mr-2" />
                角色权限
              </h4>
              <div className="space-y-2">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${roleConfig.color} w-full justify-center`}>
                  <RoleIcon className="w-4 h-4 mr-2" />
                  {roleConfig.text}
                </div>
                <p className="text-sm text-gray-600">{roleConfig.description}</p>
              </div>
            </div>

            {/* 账户状态 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                <CheckCircle className="w-4 h-4 mr-2" />
                账户状态
              </h4>
              <div className="space-y-2">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig.color} w-full justify-center`}>
                  <StatusIcon className="w-4 h-4 mr-2" />
                  {statusConfig.text}
                </div>
                <p className="text-sm text-gray-600">{statusConfig.description}</p>
              </div>
            </div>

            {/* 时间信息 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                时间信息
              </h4>
              <div className="space-y-3 text-sm">
                <div>
                  <div className="text-gray-500">创建时间</div>
                  <div className="font-medium">{formatDateTime(user.createdAt)}</div>
                  <div className="text-xs text-gray-400">{getAccountAge(user.createdAt)}</div>
                </div>
                
                {user.lastLoginAt && (
                  <div>
                    <div className="text-gray-500">最后登录</div>
                    <div className="font-medium">{formatDateTime(user.lastLoginAt)}</div>
                  </div>
                )}
                
                {user.updatedAt && (
                  <div>
                    <div className="text-gray-500">最后更新</div>
                    <div className="font-medium">{formatDateTime(user.updatedAt)}</div>
                  </div>
                )}
              </div>
            </div>

            {/* 联系信息 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                <Mail className="w-4 h-4 mr-2" />
                联系信息
              </h4>
              <div className="space-y-3 text-sm">
                <div>
                  <div className="text-gray-500">用户名</div>
                  <div className="font-medium">@{user.username}</div>
                </div>
                
                <div>
                  <div className="text-gray-500">姓名</div>
                  <div className="font-medium">{user.name}</div>
                </div>
                
                {user.email ? (
                  <div>
                    <div className="text-gray-500">邮箱</div>
                    <div className="font-medium">{user.email}</div>
                  </div>
                ) : (
                  <div>
                    <div className="text-gray-500">邮箱</div>
                    <div className="text-gray-400">未设置</div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 权限详情 */}
          <div className="mt-6 bg-blue-50 rounded-lg p-4">
            <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
              <Shield className="w-4 h-4 mr-2" />
              权限详情
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              {user.role === 'admin' && (
                <>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    用户管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    项目管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Worker管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    系统监控
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    任务管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    数据查看
                  </div>
                </>
              )}
              
              {user.role === 'operator' && (
                <>
                  <div className="flex items-center text-gray-400">
                    <XCircle className="w-4 h-4 mr-2" />
                    用户管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    项目管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Worker管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    系统监控
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    任务管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    数据查看
                  </div>
                </>
              )}
              
              {user.role === 'viewer' && (
                <>
                  <div className="flex items-center text-gray-400">
                    <XCircle className="w-4 h-4 mr-2" />
                    用户管理
                  </div>
                  <div className="flex items-center text-gray-400">
                    <XCircle className="w-4 h-4 mr-2" />
                    项目管理
                  </div>
                  <div className="flex items-center text-gray-400">
                    <XCircle className="w-4 h-4 mr-2" />
                    Worker管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    系统监控
                  </div>
                  <div className="flex items-center text-gray-400">
                    <XCircle className="w-4 h-4 mr-2" />
                    任务管理
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    数据查看
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* 底部 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div>
            {onChangePassword && (
              <button
                onClick={onChangePassword}
                className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Key className="w-4 h-4 mr-2" />
                修改密码
              </button>
            )}
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
} 