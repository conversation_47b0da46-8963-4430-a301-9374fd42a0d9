import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CopyableToken } from '@/components/ui/copyable-token'
import { 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Settings,
  RefreshCw,
  Download,
  Upload,
  Clock,
  Eye
} from 'lucide-react'

export const ProjectCard = ({ 
  project, 
  onToggle, 
  onEdit, 
  onDelete, 
  onDetail,
  actionLoading 
}) => {
  // 获取状态徽章
  const getStatusBadge = (status) => {
    const config = {
      active: { label: '运行中', variant: 'default' },
      paused: { label: '已暂停', variant: 'secondary' },
      stopped: { label: '已停止', variant: 'destructive' }
    }
    const { label, variant } = config[status] || { label: status, variant: 'outline' }
    return <Badge variant={variant}>{label}</Badge>
  }

  return (
    <Card className="relative hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{project.name}</CardTitle>
          {getStatusBadge(project.status)}
        </div>
        <CardDescription>
          创建时间: {new Date(project.createdAt).toLocaleString()}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 配置信息 */}
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex items-center">
            <Download className="w-4 h-4 mr-2 text-muted-foreground" />
            打包: {project.config.packSizeGB}GB
          </div>
          <div className="flex items-center">
            <Settings className="w-4 h-4 mr-2 text-muted-foreground" />
            并发: {project.config.concurrent}
          </div>
          <div className="flex items-center">
            <RefreshCw className="w-4 h-4 mr-2 text-muted-foreground" />
            重试: {project.config.retryTimes}次
          </div>
          <div className="flex items-center">
            <Clock className="w-4 h-4 mr-2 text-muted-foreground" />
            超时: {project.config.downloadTimeout}s
          </div>
        </div>
        
        {/* OSS信息 */}
        <div className="text-sm">
          <div className="flex items-center">
            <Upload className="w-4 h-4 mr-2 text-muted-foreground" />
            OSS: {project.config.ossConfig.provider} ({project.config.ossConfig.bucket})
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-2 pt-2">
          <Button
            size="sm"
            variant="default"
            onClick={() => onDetail(project)}
          >
            <Eye className="w-4 h-4 mr-1" />
            详情
          </Button>
          <Button
            size="sm"
            variant={project.status === 'active' ? 'outline' : 'default'}
            onClick={() => onToggle(project)}
            disabled={actionLoading}
          >
            {project.status === 'active' ? (
              <>
                <Pause className="w-4 h-4 mr-1" />
                暂停
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-1" />
                恢复
              </>
            )}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onEdit(project)}
          >
            <Edit className="w-4 h-4 mr-1" />
            编辑
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onDelete(project)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4 mr-1" />
            删除
          </Button>
        </div>

        {/* Token显示 */}
        <div className="pt-2 border-t">
          <CopyableToken 
            token={project.token} 
            label="项目Token"
            hideToken={true}
          />
        </div>
      </CardContent>
    </Card>
  )
} 