import { useState } from 'react';
import { 
  User, 
  Shield, 
  Settings, 
  Eye, 
  Mail, 
  Calendar,
  CheckCircle,
  XCircle,
  Edit,
  Trash2,
  MoreHorizontal,
  ToggleLeft,
  ToggleRight,
  Key
} from 'lucide-react';

export default function UserCard({ user, currentUserId, onView, onEdit, onDelete, onToggleStatus, onChangePassword }) {
  const [showMenu, setShowMenu] = useState(false);

  // 获取角色配置
  const getRoleConfig = (role) => {
    const configs = {
      admin: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: Shield,
        text: '管理员'
      },
      operator: {
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: Settings,
        text: '操作员'
      },
      viewer: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: Eye,
        text: '查看者'
      }
    };
    return configs[role] || configs.viewer;
  };

  // 获取状态配置
  const getStatusConfig = (status) => {
    const configs = {
      active: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle,
        text: '活跃'
      },
      inactive: {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: XCircle,
        text: '非活跃'
      },
      disabled: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: XCircle,
        text: '已禁用'
      }
    };
    return configs[status] || configs.inactive;
  };

  const roleConfig = getRoleConfig(user.role);
  const statusConfig = getStatusConfig(user.status);
  const RoleIcon = roleConfig.icon;
  const StatusIcon = statusConfig.icon;

  // 格式化时间显示
  const formatDate = (dateString) => {
    if (!dateString) return '从未';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 检查是否是当前用户
  const isCurrentUser = user.id === currentUserId;

  return (
    <div className="bg-white rounded-lg shadow border hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* 顶部：头像和基本信息 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-lg">
                {user.name?.charAt(0)?.toUpperCase() || user.username?.charAt(0)?.toUpperCase()}
              </span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {user.name}
                {isCurrentUser && (
                  <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    当前用户
                  </span>
                )}
              </h3>
              <p className="text-sm text-gray-500">@{user.username}</p>
            </div>
          </div>
          
          {/* 操作菜单 */}
          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <MoreHorizontal className="w-4 h-4 text-gray-500" />
            </button>
            
            {showMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      onView();
                      setShowMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    查看详情
                  </button>
                  <button
                    onClick={() => {
                      onEdit();
                      setShowMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    编辑用户
                  </button>
                  <button
                    onClick={() => {
                      onChangePassword();
                      setShowMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Key className="w-4 h-4 mr-2" />
                    修改密码
                  </button>
                  <div className="border-t border-gray-100 my-1"></div>
                  <button
                    onClick={() => {
                      onToggleStatus();
                      setShowMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    {user.status === 'active' ? (
                      <>
                        <ToggleLeft className="w-4 h-4 mr-2" />
                        禁用用户
                      </>
                    ) : (
                      <>
                        <ToggleRight className="w-4 h-4 mr-2" />
                        激活用户
                      </>
                    )}
                  </button>
                  {!isCurrentUser && (
                    <>
                      <div className="border-t border-gray-100 my-1"></div>
                      <button
                        onClick={() => {
                          onDelete();
                          setShowMenu(false);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        删除用户
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 角色和状态标签 */}
        <div className="flex items-center space-x-2 mb-4">
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${roleConfig.color}`}>
            <RoleIcon className="w-3 h-3 mr-1" />
            {roleConfig.text}
          </div>
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusConfig.color}`}>
            <StatusIcon className="w-3 h-3 mr-1" />
            {statusConfig.text}
          </div>
        </div>

        {/* 用户信息 */}
        <div className="space-y-2 text-sm text-gray-600">
          {user.email && (
            <div className="flex items-center">
              <Mail className="w-4 h-4 mr-2 text-gray-400" />
              <span className="truncate">{user.email}</span>
            </div>
          )}
          
          <div className="flex items-center">
            <Calendar className="w-4 h-4 mr-2 text-gray-400" />
            <span>创建时间: {formatDate(user.createdAt)}</span>
          </div>
          
          {user.lastLoginAt && (
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2 text-gray-400" />
              <span>最后登录: {formatDate(user.lastLoginAt)}</span>
            </div>
          )}
        </div>

        {/* 快速操作按钮 */}
        <div className="flex items-center space-x-2 mt-4 pt-4 border-t border-gray-100">
          <button
            onClick={onView}
            className="flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            <Eye className="w-4 h-4 mr-1" />
            查看
          </button>
          <button
            onClick={onEdit}
            className="flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors"
          >
            <Edit className="w-4 h-4 mr-1" />
            编辑
          </button>
        </div>
      </div>
      
      {/* 点击遮罩关闭菜单 */}
      {showMenu && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
} 