import { useState } from 'react';
import { 
  Server, 
  Activity, 
  HardDrive, 
  Wifi, 
  Clock,
  AlertTriangle,
  CheckCircle,
  Eye,
  Copy,
  ExternalLink
} from 'lucide-react';
import { CopyableToken } from './ui/copyable-token';

export default function WorkerCard({ worker, onClick }) {
  const [copied, setCopied] = useState(false);

  // 获取状态配置
  const getStatusConfig = (status) => {
    const configs = {
      pending: {
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: Clock,
        text: '等待激活'
      },
      active: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle,
        text: '正常工作'
      },
      offline: {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: AlertTriangle,
        text: '离线'
      },
      error: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: AlertTriangle,
        text: '错误'
      },
      disabled: {
        color: 'bg-gray-100 text-gray-600 border-gray-200',
        icon: AlertTriangle,
        text: '已禁用'
      }
    };
    return configs[status] || configs.offline;
  };

  const statusConfig = getStatusConfig(worker.status);
  const StatusIcon = statusConfig.icon;

  // 获取资源使用率颜色
  const getUsageColor = (usage) => {
    if (usage >= 90) return 'text-red-600';
    if (usage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  // 格式化时间显示
  const formatTimeAgo = (timestamp) => {
    if (!timestamp) return '从未';
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);
    
    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    return `${Math.floor(diffInSeconds / 86400)}天前`;
  };

  // 格式化上线时间
  const formatUptime = (uptime) => {
    if (!uptime) return '未知';
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  // 复制Token
  const copyToken = async (e) => {
    e.stopPropagation();
    if (worker.token) {
      try {
        await navigator.clipboard.writeText(worker.token);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy token:', error);
      }
    }
  };

  return (
    <div 
      className="bg-white rounded-lg shadow border hover:shadow-md transition-shadow cursor-pointer"
      onClick={onClick}
    >
      <div className="p-6">
        {/* 顶部：名称和状态 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Server className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{worker.name}</h3>
              {worker.description && (
                <p className="text-sm text-gray-500 truncate max-w-[200px]">{worker.description}</p>
              )}
            </div>
          </div>
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusConfig.color}`}>
            <StatusIcon className="w-3 h-3 mr-1" />
            {statusConfig.text}
          </div>
        </div>

        {/* 基本信息 */}
        <div className="space-y-3 mb-4">
          {worker.status === 'pending' ? (
            <div className="text-sm text-gray-500 space-y-1">
              <div>等待 Worker 激活</div>
              <div>预期能力: {worker.capabilities?.maxConcurrent || 0} 并发</div>
            </div>
          ) : (
            <>
              {worker.host && worker.port && (
                <div className="flex items-center text-sm text-gray-600">
                  <ExternalLink className="w-4 h-4 mr-2 text-gray-400" />
                  <span>{worker.host}:{worker.port}</span>
                  {worker.version && (
                    <span className="ml-2 text-gray-400">v{worker.version}</span>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        {/* 系统资源（仅对已激活的 Worker 显示） */}
        {worker.status === 'active' && worker.stats && (
          <div className="space-y-3 mb-4">
            <div className="grid grid-cols-3 gap-3">
              <div className="text-center">
                <div className={`text-lg font-semibold ${getUsageColor(worker.stats.cpuUsage)}`}>
                  {worker.stats.cpuUsage?.toFixed(1) || 0}%
                </div>
                <div className="text-xs text-gray-500">CPU</div>
              </div>
              <div className="text-center">
                <div className={`text-lg font-semibold ${getUsageColor(worker.stats.memoryUsage)}`}>
                  {worker.stats.memoryUsage?.toFixed(1) || 0}%
                </div>
                <div className="text-xs text-gray-500">内存</div>
              </div>
              <div className="text-center">
                <div className={`text-lg font-semibold ${getUsageColor(worker.stats.diskUsage)}`}>
                  {worker.stats.diskUsage?.toFixed(1) || 0}%
                </div>
                <div className="text-xs text-gray-500">磁盘</div>
              </div>
            </div>

            {/* 任务统计 */}
            <div className="pt-3 border-t border-gray-100">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">活跃任务: </span>
                  <span className="font-medium text-blue-600">{worker.stats.activeTasks || 0}</span>
                </div>
                <div>
                  <span className="text-gray-500">总处理: </span>
                  <span className="font-medium">{(worker.stats.totalProcessed || 0).toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Worker 能力配置 */}
        <div className="space-y-2 mb-4">
          <h4 className="text-sm font-medium text-gray-700">能力配置</h4>
          <div className="grid grid-cols-3 gap-2 text-xs text-gray-600">
            <div className="flex items-center">
              <Activity className="w-3 h-3 mr-1 text-gray-400" />
              <span>{worker.capabilities?.maxConcurrent || 0} 并发</span>
            </div>
            <div className="flex items-center">
              <HardDrive className="w-3 h-3 mr-1 text-gray-400" />
              <span>{worker.capabilities?.diskSpace || 0} GB</span>
            </div>
            <div className="flex items-center">
              <Wifi className="w-3 h-3 mr-1 text-gray-400" />
              <span>{worker.capabilities?.bandwidth || 0} Mbps</span>
            </div>
          </div>
        </div>

        {/* 健康状态和时间信息 */}
        <div className="space-y-2 mb-4">
          {worker.health?.uptime && (
            <div className="flex justify-between text-xs text-gray-500">
              <span>运行时间:</span>
              <span>{formatUptime(worker.health.uptime)}</span>
            </div>
          )}
          <div className="flex justify-between text-xs text-gray-500">
            <span>最后心跳:</span>
            <span>{formatTimeAgo(worker.lastHeartbeat)}</span>
          </div>
          {worker.activatedAt && (
            <div className="flex justify-between text-xs text-gray-500">
              <span>激活时间:</span>
              <span>{formatTimeAgo(worker.activatedAt)}</span>
            </div>
          )}
        </div>

        {/* Token 显示（仅对新创建的 Worker） */}
        {worker.token && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="text-xs text-yellow-800 font-medium mb-2">注册 Token:</div>
            <CopyableToken token={worker.token} />
          </div>
        )}

        {/* 警告信息 */}
        {worker.status === 'active' && worker.stats && (
          <>
            {(worker.stats.cpuUsage > 90 || worker.stats.memoryUsage > 90 || worker.stats.diskUsage > 90) && (
              <div className="mb-4 p-2 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center text-xs text-red-800">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  <span>资源使用率过高</span>
                </div>
              </div>
            )}
          </>
        )}

        {/* 底部操作 */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="text-xs text-gray-500">
            ID: {worker.id?.substring(0, 8) || 'N/A'}...
          </div>
          <button 
            className="flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onClick();
            }}
          >
            <Eye className="w-3 h-3" />
            <span>查看详情</span>
          </button>
        </div>
      </div>
    </div>
  );
} 