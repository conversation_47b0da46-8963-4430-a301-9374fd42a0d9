import { useState } from 'react';
import { 
  Server, 
  Activity, 
  HardDrive, 
  Wifi, 
  Clock,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  MonitorSpeaker,
  Cpu,
  MemoryStick,
  Calendar,
  Timer,
  Zap
} from 'lucide-react';
import { CopyableToken } from './ui/copyable-token';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from './ui/sheet';

export default function WorkerDetailSheet({ worker, isOpen, onClose }) {
  // 获取状态配置
  const getStatusConfig = (status) => {
    const configs = {
      pending: {
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: Clock,
        text: '等待激活',
        description: 'Worker 已预注册，等待启动激活'
      },
      active: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle,
        text: '正常工作',
        description: 'Worker 正在正常运行并处理任务'
      },
      offline: {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: AlertTriangle,
        text: '离线',
        description: 'Worker 已离线或心跳超时'
      },
      error: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: AlertTriangle,
        text: '错误',
        description: 'Worker 运行出现错误'
      },
      disabled: {
        color: 'bg-gray-100 text-gray-600 border-gray-200',
        icon: AlertTriangle,
        text: '已禁用',
        description: 'Worker 已被管理员禁用'
      }
    };
    return configs[status] || configs.offline;
  };

  const statusConfig = getStatusConfig(worker?.status);
  const StatusIcon = statusConfig?.icon;

  // 获取资源使用率颜色和进度条样式
  const getUsageStyle = (usage) => {
    const percentage = Math.min(100, Math.max(0, usage || 0));
    let colorClass = 'bg-green-500';
    
    if (percentage >= 90) colorClass = 'bg-red-500';
    else if (percentage >= 75) colorClass = 'bg-yellow-500';
    
    return { percentage, colorClass };
  };

  // 格式化时间显示
  const formatTimeAgo = (timestamp) => {
    if (!timestamp) return '从未';
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);
    
    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    return `${Math.floor(diffInSeconds / 86400)}天前`;
  };

  // 格式化上线时间
  const formatUptime = (uptime) => {
    if (!uptime) return '未知';
    const days = Math.floor(uptime / 86400);
    const hours = Math.floor((uptime % 86400) / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    
    if (days > 0) {
      return `${days}天${hours}小时${minutes}分钟`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  // 格式化日期
  const formatDate = (timestamp) => {
    if (!timestamp) return '未知';
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-full max-w-[800px] sm:max-w-[800px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="flex items-center">
            <Server className="w-6 h-6 mr-3" />
            {worker?.name}
          </SheetTitle>
          {worker?.description && (
            <SheetDescription>
              {worker.description}
            </SheetDescription>
          )}
        </SheetHeader>

        <div className="space-y-6 mt-6">
          {/* 状态信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">状态信息</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">当前状态:</span>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig?.color}`}>
                  {StatusIcon && <StatusIcon className="w-4 h-4 mr-2" />}
                  {statusConfig?.text}
                </div>
              </div>
              <div className="text-sm text-gray-500">
                {statusConfig?.description}
              </div>
            </div>
          </div>

          {/* 基本信息 */}
          <div className="bg-white border rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <ExternalLink className="w-5 h-5 mr-2" />
              基本信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Worker ID</label>
                <div className="mt-1 font-mono text-sm text-gray-900">{worker?.id}</div>
              </div>
              
              {worker?.status === 'pending' ? (
                <>
                  <div>
                    <label className="text-sm font-medium text-gray-500">状态</label>
                    <div className="mt-1 text-sm text-yellow-600">等待 Worker 启动激活</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">网络地址</label>
                    <div className="mt-1 text-sm text-gray-500">待激活后获取</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">版本</label>
                    <div className="mt-1 text-sm text-gray-500">待激活后获取</div>
                  </div>
                </>
              ) : (
                <>
                  {worker?.host && worker?.port && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">网络地址</label>
                      <div className="mt-1 text-sm text-gray-900">{worker.host}:{worker.port}</div>
                    </div>
                  )}
                  {worker?.version && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">版本</label>
                      <div className="mt-1 text-sm text-gray-900">v{worker.version}</div>
                    </div>
                  )}
                </>
              )}
              
              <div>
                <label className="text-sm font-medium text-gray-500">创建时间</label>
                <div className="mt-1 text-sm text-gray-900">{formatDate(worker?.createdAt)}</div>
              </div>
              
              {worker?.activatedAt && (
                <div>
                  <label className="text-sm font-medium text-gray-500">激活时间</label>
                  <div className="mt-1 text-sm text-gray-900">{formatDate(worker.activatedAt)}</div>
                </div>
              )}
            </div>
          </div>

          {/* 系统信息（仅激活后显示） */}
          {worker?.systemInfo && (
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <MonitorSpeaker className="w-5 h-5 mr-2" />
                系统信息
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">操作系统</label>
                  <div className="mt-1 text-sm text-gray-900">
                    {worker.systemInfo.os} ({worker.systemInfo.arch})
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">CPU 核心数</label>
                  <div className="mt-1 text-sm text-gray-900">{worker.systemInfo.cpuCores} 核</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">总内存</label>
                  <div className="mt-1 text-sm text-gray-900">{worker.systemInfo.totalMemory} MB</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">总磁盘空间</label>
                  <div className="mt-1 text-sm text-gray-900">{worker.systemInfo.totalDisk} GB</div>
                </div>
              </div>
            </div>
          )}

          {/* 能力配置 */}
          <div className="bg-white border rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              能力配置
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <Activity className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">
                    {worker?.capabilities?.maxConcurrent || 0}
                  </div>
                  <div className="text-sm text-gray-500">最大并发数</div>
                </div>
              </div>
              <div className="text-center">
                <div className="p-4 bg-green-50 rounded-lg">
                  <HardDrive className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">
                    {worker?.capabilities?.diskSpace || 0}
                  </div>
                  <div className="text-sm text-gray-500">磁盘空间 (GB)</div>
                </div>
              </div>
              <div className="text-center">
                <div className="p-4 bg-purple-50 rounded-lg">
                  <Wifi className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">
                    {worker?.capabilities?.bandwidth || 0}
                  </div>
                  <div className="text-sm text-gray-500">带宽 (Mbps)</div>
                </div>
              </div>
            </div>
          </div>

          {/* 系统资源（仅激活状态显示） */}
          {worker?.status === 'active' && worker?.stats && (
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                系统资源
              </h3>
              <div className="space-y-4">
                {/* CPU */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <Cpu className="w-4 h-4 mr-2 text-gray-600" />
                      <span className="text-sm font-medium text-gray-700">CPU 使用率</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {worker.stats.cpuUsage?.toFixed(1) || 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getUsageStyle(worker.stats.cpuUsage).colorClass}`}
                      style={{ width: `${getUsageStyle(worker.stats.cpuUsage).percentage}%` }}
                    ></div>
                  </div>
                </div>

                {/* 内存 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <MemoryStick className="w-4 h-4 mr-2 text-gray-600" />
                      <span className="text-sm font-medium text-gray-700">内存使用率</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {worker.stats.memoryUsage?.toFixed(1) || 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getUsageStyle(worker.stats.memoryUsage).colorClass}`}
                      style={{ width: `${getUsageStyle(worker.stats.memoryUsage).percentage}%` }}
                    ></div>
                  </div>
                </div>

                {/* 磁盘 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <HardDrive className="w-4 h-4 mr-2 text-gray-600" />
                      <span className="text-sm font-medium text-gray-700">磁盘使用率</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {worker.stats.diskUsage?.toFixed(1) || 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getUsageStyle(worker.stats.diskUsage).colorClass}`}
                      style={{ width: `${getUsageStyle(worker.stats.diskUsage).percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* 资源警告 */}
              {(worker.stats.cpuUsage > 90 || worker.stats.memoryUsage > 90 || worker.stats.diskUsage > 90) && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-center text-red-800">
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">资源使用率过高警告</span>
                  </div>
                  <div className="text-xs text-red-700 mt-1">
                    建议检查 Worker 负载情况，必要时减少任务分配或升级硬件配置
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 工作统计（仅激活状态显示） */}
          {worker?.status === 'active' && worker?.stats && (
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">工作统计</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {worker.stats.activeTasks || 0}
                  </div>
                  <div className="text-sm text-gray-600">活跃任务</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {(worker.stats.totalProcessed || 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">总处理数</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {(worker.stats.totalFailed || 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">失败数</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-600">
                    {worker.stats.totalProcessed > 0 
                      ? ((1 - (worker.stats.totalFailed || 0) / worker.stats.totalProcessed) * 100).toFixed(1)
                      : 0
                    }%
                  </div>
                  <div className="text-sm text-gray-600">成功率</div>
                </div>
              </div>
            </div>
          )}

          {/* 健康状态 */}
          {worker?.health && (
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Timer className="w-5 h-5 mr-2" />
                健康状态
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">健康状态:</span>
                  <span className={`font-medium ${
                    worker.health.status === 'healthy' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {worker.health.status === 'healthy' ? '健康' : '异常'}
                  </span>
                </div>
                {worker.health.uptime && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">运行时间:</span>
                    <span className="font-medium">{formatUptime(worker.health.uptime)}</span>
                  </div>
                )}
                {worker.health.lastError && (
                  <div>
                    <span className="text-gray-600">最后错误:</span>
                    <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-800">
                      {worker.health.lastError}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 时间信息 */}
          <div className="bg-white border rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              时间信息
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">最后心跳:</span>
                <span className="font-medium">{formatTimeAgo(worker?.lastHeartbeat)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">创建时间:</span>
                <span className="font-medium">{formatDate(worker?.createdAt)}</span>
              </div>
              {worker?.activatedAt && (
                <div className="flex justify-between">
                  <span className="text-gray-600">激活时间:</span>
                  <span className="font-medium">{formatDate(worker.activatedAt)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Token 管理（仅对有 token 的 worker 显示） */}
          {worker?.token && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-yellow-800 mb-4">Token 管理</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-yellow-700">Worker Token</label>
                  <div className="mt-2">
                    <CopyableToken token={worker.token} />
                  </div>
                </div>
                <div className="text-sm text-yellow-700">
                  <strong>使用方法:</strong> 在 Worker 服务器上运行以下命令
                </div>
                <div className="bg-black rounded-md p-3 text-green-400 font-mono text-sm">
                  ./worker --token={worker.token} --port=8081
                </div>
              </div>
            </div>
          )}

          {/* 项目分配（如果有的话） */}
          {worker?.projects && worker.projects.length > 0 && (
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">项目分配</h3>
              <div className="space-y-2">
                {worker.projects.map((project, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="font-medium">{project.name || project}</span>
                    <span className="text-sm text-gray-500">已分配</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
} 