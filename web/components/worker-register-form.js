import { useState } from 'react';
import { X, Co<PERSON>, Check } from 'lucide-react';
import { useToast } from '../hooks/use-toast';
import { workersAPI, apiUtils } from '../services/api';

export default function WorkerRegisterForm({ isOpen, onClose, onSuccess }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    capabilities: {
      maxConcurrent: 10,
      diskSpace: 500,
      bandwidth: 1000
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [registrationResult, setRegistrationResult] = useState(null);
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await workersAPI.registerWorker(formData);
      setRegistrationResult(response.data);
      toast({
        title: "Worker 预注册成功",
        description: "请将注册Token提供给运维人员部署Worker",
      });
    } catch (error) {
      console.error('Error registering worker:', error);
      const errorInfo = apiUtils.handleError(error);
      
      // 如果是已处理的401错误，不显示错误消息（因为会自动跳转到登录页面）
      if (!errorInfo.handled) {
        toast({
          title: "注册失败",
          description: errorInfo.message,
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const copyToken = async () => {
    if (registrationResult?.token) {
      try {
        await navigator.clipboard.writeText(registrationResult.token);
        setCopied(true);
        toast({
          title: "复制成功",
          description: "注册Token已复制到剪贴板",
        });
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        toast({
          title: "复制失败",
          description: "请手动复制Token",
          variant: "destructive",
        });
      }
    }
  };

  const handleClose = () => {
    if (registrationResult && onSuccess) {
      onSuccess(registrationResult);
    }
    onClose();
    setFormData({
      name: '',
      description: '',
      capabilities: {
        maxConcurrent: 10,
        diskSpace: 500,
        bandwidth: 1000
      }
    });
    setRegistrationResult(null);
    setCopied(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black/20" onClick={handleClose} />
      <div className="relative bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">
            {registrationResult ? 'Worker 注册成功' : '预注册 Worker'}
          </h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        <div className="p-6">
          {registrationResult ? (
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="font-medium text-green-800 mb-2">
                  预注册完成！
                </h3>
                <p className="text-sm text-green-700 mb-4">
                  Worker 已预注册成功，请按照以下步骤完成部署：
                </p>
                <ol className="text-sm text-green-700 space-y-2 list-decimal list-inside">
                  <li>将下面的Token提供给运维人员</li>
                  <li>在目标服务器上部署Worker程序</li>
                  <li>使用Token启动Worker服务</li>
                  <li>Worker将自动激活并上报系统信息</li>
                </ol>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Worker ID
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md font-mono text-sm">
                    {registrationResult.id}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    注册Token
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 p-3 bg-gray-50 rounded-md font-mono text-sm break-all">
                      {registrationResult.token}
                    </div>
                    <button
                      onClick={copyToken}
                      className="p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center space-x-1"
                    >
                      {copied ? (
                        <Check className="w-4 h-4" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-medium text-yellow-800 mb-2">部署命令示例</h4>
                <div className="bg-black rounded-md p-3 text-green-400 font-mono text-sm">
                  ./worker --token={registrationResult.token} --port=8081
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 mb-2">配置预览</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <div><strong>名称:</strong> {formData.name}</div>
                  <div><strong>描述:</strong> {formData.description}</div>
                  <div><strong>最大并发:</strong> {formData.capabilities.maxConcurrent}</div>
                  <div><strong>磁盘空间:</strong> {formData.capabilities.diskSpace} GB</div>
                  <div><strong>带宽:</strong> {formData.capabilities.bandwidth} Mbps</div>
                </div>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-800 mb-2">预注册说明</h3>
                <p className="text-sm text-blue-700">
                  这里仅创建Worker的预注册信息，实际的主机地址、端口和版本信息会在Worker启动时自动上报。
                  请填写Worker的基本信息和预期能力配置。
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Worker 名称 *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="例如: hk-worker-01"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述信息
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="例如: 香港服务器"
                    rows={3}
                  />
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">预期能力配置</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      最大并发数
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="100"
                      value={formData.capabilities.maxConcurrent}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        capabilities: {
                          ...prev.capabilities,
                          maxConcurrent: parseInt(e.target.value) || 10
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      磁盘空间 (GB)
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.capabilities.diskSpace}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        capabilities: {
                          ...prev.capabilities,
                          diskSpace: parseInt(e.target.value) || 500
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      带宽 (Mbps)
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.capabilities.bandwidth}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        capabilities: {
                          ...prev.capabilities,
                          bandwidth: parseInt(e.target.value) || 1000
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-6 border-t">
                <button
                  type="button"
                  onClick={handleClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {isSubmitting ? '预注册中...' : '创建预注册'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
} 