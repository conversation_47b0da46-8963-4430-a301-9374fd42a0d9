import { useSession, signOut } from 'next-auth/react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { 
  LayoutDashboard, 
  FolderOpen, 
  Users, 
  Settings, 
  LogOut,
  Download,
  Menu
} from 'lucide-react'
import { cn } from '@/lib/utils'

const Layout = ({ children }) => {
  const { data: session, status } = useSession()
  const router = useRouter()

  // 公开路径，不需要认证
  const publicPaths = ['/auth/signin', '/auth/error']
  const isPublicPath = publicPaths.includes(router.pathname)

  // 如果未认证且不在公开路径，重定向到登录页
  useEffect(() => {
    if (status === 'loading') return // 仍在加载

    if (!session && !isPublicPath) {
      router.push('/auth/signin')
    }
  }, [session, status, router, isPublicPath])

  // 如果是公开路径或未认证，直接渲染children
  if (isPublicPath || !session) {
    return <>{children}</>
  }

  const navigation = [
    { name: '控制台', href: '/', icon: LayoutDashboard },
    { name: '项目管理', href: '/projects', icon: FolderOpen },
    { name: 'Worker管理', href: '/workers', icon: Users },
    { name: '用户管理', href: '/users', icon: Users },
    // { name: '系统设置', href: '/settings', icon: Settings },
  ]

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' })
  }

  return (
    <div className="flex h-screen bg-background">
      {/* 侧边栏 */}
      <aside className="w-64 bg-card border-r border-border shadow-sm">
        <div className="p-6">
          <div className="flex items-center space-x-2">
            <Download className="h-8 w-8 text-primary" />
            <h1 className="text-xl font-bold text-foreground">下载调度系统</h1>
          </div>
        </div>
        
        <nav className="mt-6 px-3">
          <ul className="space-y-1">
            {navigation.map((item) => {
              const isActive = router.pathname === item.href
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                      isActive
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:text-foreground hover:bg-accent"
                    )}
                  >
                    <item.icon className="mr-3 h-4 w-4" />
                    {item.name}
                  </Link>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* 用户信息和退出 */}
        <div className="absolute bottom-0 left-0 right-0 w-64 p-4 border-t border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-foreground">{session.user.name}</p>
              <p className="text-xs text-muted-foreground">{session.user.role}</p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleSignOut}
              title="退出登录"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </aside>

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部栏 */}
        <header className="bg-card border-b border-border px-6 py-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-foreground">
              {navigation.find(item => item.href === router.pathname)?.name || '控制台'}
            </h2>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                欢迎回来，{session.user.name}
              </span>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <div className="flex-1 overflow-auto p-6">
          {children}
        </div>
      </main>
    </div>
  )
}

export default Layout
