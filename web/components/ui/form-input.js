import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export const FormInput = ({ 
  label, 
  value, 
  onChange, 
  type = "text", 
  required = false, 
  placeholder,
  disabled = false,
  className,
  ...props
}) => (
  <div className="space-y-2">
    {label && (
      <Label className="text-sm font-medium">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
    )}
    <Input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      required={required}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      {...props}
    />
  </div>
) 