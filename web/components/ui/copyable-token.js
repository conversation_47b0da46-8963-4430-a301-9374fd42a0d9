import { useState } from 'react'
import { Button } from './button'
import { Label } from './label'
import { useToast } from '../../hooks/use-toast'
import { Copy, Check, Eye, EyeOff } from 'lucide-react'

export const CopyableToken = ({ 
  token, 
  label = "Token", 
  className = "",
  hideToken = true 
}) => {
  const [copied, setCopied] = useState(false)
  const [showToken, setShowToken] = useState(!hideToken)
  const { toast } = useToast()

  // 复制到剪贴板
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(token)
      setCopied(true)
      toast({
        title: "复制成功",
        description: "Token 已复制到剪贴板"
      })
      
      // 2秒后重置复制状态
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive"
      })
    }
  }

  // 切换显示/隐藏 token
  const toggleShowToken = () => {
    setShowToken(!showToken)
  }

  // 隐藏 token 的显示格式
  const getDisplayToken = () => {
    if (showToken) {
      return token
    }
    // 只显示前8位和后4位，中间用*代替
    if (token && token.length > 12) {
      return `${token.substring(0, 8)}${'*'.repeat(token.length - 12)}${token.substring(token.length - 4)}`
    }
    return token
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <Label className="text-xs text-muted-foreground">{label}:</Label>
      <div className="flex items-center space-x-2">
        <div className="flex-1 p-2 bg-muted rounded text-xs font-mono break-all">
          {getDisplayToken()}
        </div>
        <div className="flex space-x-1">
          {hideToken && (
            <Button
              size="sm"
              variant="ghost"
              onClick={toggleShowToken}
              className="h-8 w-8 p-0"
              title={showToken ? "隐藏 Token" : "显示 Token"}
            >
              {showToken ? (
                <EyeOff className="w-3 h-3" />
              ) : (
                <Eye className="w-3 h-3" />
              )}
            </Button>
          )}
          <Button
            size="sm"
            variant="ghost"
            onClick={handleCopy}
            className="h-8 w-8 p-0"
            title="复制 Token"
          >
            {copied ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>
    </div>
  )
} 