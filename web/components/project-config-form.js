import { FormInput } from '@/components/ui/form-input'
import { Label } from '@/components/ui/label'

export const ProjectConfigForm = ({ formData, setFormData, mode = 'create' }) => {
  const updateConfig = (field, value) => {
    setFormData({
      ...formData,
      config: { ...formData.config, [field]: value }
    })
  }

  const updateOSSConfig = (field, value) => {
    setFormData({
      ...formData,
      config: {
        ...formData.config,
        ossConfig: { ...formData.config.ossConfig, [field]: value }
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* 基本信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">基本信息</h3>
        <FormInput
          label="项目名称(唯一）"
          value={formData.name}
          onChange={(value) => setFormData({...formData, name: value})}
          placeholder="请输入项目名称"
          required
          disabled={mode === 'edit'}
        />
        
        <div className="grid grid-cols-2 gap-4">
          <FormInput
            label="打包大小(GB)"
            type="number"
            value={formData.config.packSizeGB}
            onChange={(value) => updateConfig('packSizeGB', parseInt(value) || 10)}
            placeholder="GB"
          />
          <FormInput
            label="并发数"
            type="number"
            value={formData.config.concurrent}
            onChange={(value) => updateConfig('concurrent', parseInt(value) || 5)}
            placeholder="同时下载数"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormInput
            label="重试次数"
            type="number"
            value={formData.config.retryTimes}
            onChange={(value) => updateConfig('retryTimes', parseInt(value) || 3)}
            placeholder="失败重试次数"
          />
          <FormInput
            label="下载超时(s)"
            type="number"
            value={formData.config.downloadTimeout}
            onChange={(value) => updateConfig('downloadTimeout', parseInt(value) || 300)}
            placeholder="秒"
          />
        </div>
      </div>

      {/* OSS配置 */}
      <div className="space-y-4">
        <div className="border-t pt-4">
          <h3 className="text-lg font-semibold mb-4">OSS 存储配置</h3>
          <div className="grid grid-cols-2 gap-4">
            <FormInput
              label="服务商(备注）"
              value={formData.config.ossConfig.provider}
              onChange={(value) => updateOSSConfig('provider', value)}
              placeholder="aliyun / aws / minio"
            />
            <FormInput
              label="端点地址"
              value={formData.config.ossConfig.endpoint}
              onChange={(value) => updateOSSConfig('endpoint', value)}
              placeholder="oss-cn-beijing.aliyuncs.com"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <FormInput
              label="存储桶名称"
              value={formData.config.ossConfig.bucket}
              onChange={(value) => updateOSSConfig('bucket', value)}
              placeholder="bucket-name"
              required
            />
            <FormInput
              label="地域"
              value={formData.config.ossConfig.region}
              onChange={(value) => updateOSSConfig('region', value)}
              placeholder="cn-beijing"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <FormInput
              label="Access Key"
              value={formData.config.ossConfig.accessKey}
              onChange={(value) => updateOSSConfig('accessKey', value)}
              placeholder="访问密钥ID"
              required
            />
            <FormInput
              label="Secret Key"
              type="password"
              value={formData.config.ossConfig.secretKey}
              onChange={(value) => updateOSSConfig('secretKey', value)}
              placeholder="访问密钥Secret"
              required
            />
          </div>
          
          <FormInput
            label="路径前缀"
            value={formData.config.ossConfig.prefix}
            onChange={(value) => updateOSSConfig('prefix', value)}
            placeholder="project1/ (可选)"
          />
          
          {/* ForcePathStyle 复选框 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">路径样式</Label>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="forcePathStyle"
                checked={formData.config.ossConfig.forcePathStyle}
                onChange={(e) => updateOSSConfig('forcePathStyle', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <Label htmlFor="forcePathStyle" className="text-sm text-gray-700">
                强制使用路径样式 (MinIO 需要此选项)
              </Label>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 