#!/bin/bash
set -e

echo "Setting up development environment for download-scheduler project..."

# Update system packages
sudo apt-get update

# Install Go 1.23
echo "Installing Go 1.23..."
cd /tmp
wget -q https://go.dev/dl/go1.23.4.linux-amd64.tar.gz
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf go1.23.4.linux-amd64.tar.gz

# Add Go to PATH in profile
echo 'export PATH=$PATH:/usr/local/go/bin' >> $HOME/.profile
echo 'export GOPATH=$HOME/go' >> $HOME/.profile
echo 'export PATH=$PATH:$GOPATH/bin' >> $HOME/.profile
source $HOME/.profile

# Install Node.js 20
echo "Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installations
echo "Verifying installations..."
/usr/local/go/bin/go version
node --version
npm --version

# Navigate to workspace
cd /mnt/persist/workspace

# Setup Go server module
echo "Setting up Go server dependencies..."
cd server
/usr/local/go/bin/go mod download
/usr/local/go/bin/go mod tidy
cd ..

# Setup Go worker module  
echo "Setting up Go worker dependencies..."
cd worker
/usr/local/go/bin/go mod download
/usr/local/go/bin/go mod tidy
cd ..

# Setup Node.js web dependencies
echo "Setting up Node.js web dependencies..."
cd web
npm install
cd ..

echo "Development environment setup complete!"