#!/bin/bash
set -e

echo "Setting up development environment for download-scheduler project..."

# Update system packages
sudo apt-get update

# Install Go 1.23
echo "Installing Go 1.23..."
cd /tmp
wget -q https://go.dev/dl/go1.23.4.linux-amd64.tar.gz
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf go1.23.4.linux-amd64.tar.gz

# Add Go to PATH in profile
echo 'export PATH=$PATH:/usr/local/go/bin' >> $HOME/.profile
echo 'export GOPATH=$HOME/go' >> $HOME/.profile
echo 'export PATH=$PATH:$GOPATH/bin' >> $HOME/.profile
source $HOME/.profile

# Install Node.js 20
echo "Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installations
echo "Verifying installations..."
/usr/local/go/bin/go version
node --version
npm --version

# Navigate to workspace
cd /mnt/persist/workspace

# Setup Go server module
echo "Setting up Go server dependencies..."
cd server
/usr/local/go/bin/go mod download
/usr/local/go/bin/go mod tidy
cd ..

# Setup Go worker module  
echo "Setting up Go worker dependencies..."
cd worker
/usr/local/go/bin/go mod download
/usr/local/go/bin/go mod tidy
cd ..

# Setup Node.js web dependencies
echo "Setting up Node.js web dependencies..."
cd web
npm install
cd ..

# Install Docker and Docker Compose for middleware
echo "Installing Docker and Docker Compose..."
# Update package list
sudo apt-get update

# Install prerequisites
sudo apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# Add Docker's official GPG key
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Set up the repository
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
sudo apt-get update
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Add user to docker group
sudo usermod -aG docker $USER

# Install Docker Compose (standalone)
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker

# Verify Docker installation
echo "Verifying Docker installation..."
sudo docker --version
sudo docker-compose --version

# Setup middleware
echo "Setting up development middleware..."
# Make middleware setup script executable
chmod +x scripts/setup-middleware.sh

# Start middleware services
echo "Starting middleware services (MongoDB, Redis, RabbitMQ)..."
sudo -u $USER bash -c 'cd /mnt/persist/workspace && ./scripts/setup-middleware.sh start'

# Wait for services to be ready
echo "Waiting for middleware services to be ready..."
sleep 30

# Verify middleware is running
echo "Verifying middleware services..."
sudo docker-compose ps

echo "Development environment setup complete!"
echo ""
echo "Services started:"
echo "  - MongoDB: localhost:27017 (admin/password123)"
echo "  - Redis: localhost:6379 (password: password123)"
echo "  - RabbitMQ: localhost:5672 (admin/password123)"
echo "  - RabbitMQ Management: http://localhost:15672"
echo ""
echo "To start the application services:"
echo "  1. Server: cd server && make dev"
echo "  2. Worker: cd worker && make dev"
echo "  3. Web: cd web && npm run dev"
echo ""
echo "For more information, see docs/dev.md"