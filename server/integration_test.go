package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"download-scheduler/server/internal/config"
)

func TestServerBasicFunctionality(t *testing.T) {
	// 加载配置
	cfg, err := config.LoadConfig("configs/config.yaml")
	if err != nil {
		t.Skipf("Skipping integration test - config file not found: %v", err)
	}

	// 验证配置加载
	if cfg.Server.Port != 8080 {
		t.<PERSON>rrorf("Expected port 8080, got %d", cfg.Server.Port)
	}

	if cfg.Server.Mode != "debug" {
		t.<PERSON>rrorf("Expected mode debug, got %s", cfg.Server.Mode)
	}

	if cfg.Database.MongoDB.Database != "download_scheduler_test" {
		t.Errorf("Expected database download_scheduler_test, got %s", cfg.Database.MongoDB.Database)
	}
}

func TestServerHealthEndpoint(t *testing.T) {
	// 这个测试需要实际运行的server，在没有server的情况下跳过
	t.Skip("Skipping server health test - requires running server")

	// 如果server在运行，可以使用以下代码：
	/*
		resp, err := http.Get("http://localhost:8080/health")
		if err != nil {
			t.Skipf("Server not running: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != 200 {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}
	*/
}

func TestAPIEndpointStructure(t *testing.T) {
	// 测试API请求结构体的创建和序列化
	projectReq := map[string]interface{}{
		"name": "Test Integration Project",
		"config": map[string]interface{}{
			"packSizeGB":      10,
			"concurrent":      5,
			"retryTimes":      3,
			"downloadTimeout": 300,
			"ossConfig": map[string]interface{}{
				"provider":       "aliyun",
				"endpoint":       "oss-cn-beijing.aliyuncs.com",
				"bucket":         "test-bucket",
				"accessKey":      "test-key",
				"secretKey":      "test-secret",
				"prefix":         "test/",
				"region":         "cn-beijing",
				"forcePathStyle": false,
			},
		},
	}

	jsonData, err := json.Marshal(projectReq)
	if err != nil {
		t.Fatalf("Failed to marshal project request: %v", err)
	}

	// 验证JSON结构
	var decoded map[string]interface{}
	err = json.Unmarshal(jsonData, &decoded)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	if decoded["name"] != "Test Integration Project" {
		t.Errorf("Expected name 'Test Integration Project', got %v", decoded["name"])
	}

	config := decoded["config"].(map[string]interface{})
	if config["packSizeGB"].(float64) != 10 {
		t.Errorf("Expected packSizeGB 10, got %v", config["packSizeGB"])
	}
}

func TestWorkerRegistrationData(t *testing.T) {
	// 测试Worker注册数据结构
	workerReq := map[string]interface{}{
		"name":    "integration-test-worker",
		"host":    "*************",
		"port":    8081,
		"version": "1.0.0",
		"capabilities": map[string]interface{}{
			"maxConcurrent": 10,
			"diskSpace":     500,
			"bandwidth":     1000,
		},
	}

	jsonData, err := json.Marshal(workerReq)
	if err != nil {
		t.Fatalf("Failed to marshal worker request: %v", err)
	}

	// 模拟HTTP请求
	req, err := http.NewRequest("POST", "/api/workers/register", bytes.NewBuffer(jsonData))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	if req.Header.Get("Content-Type") != "application/json" {
		t.Error("Content-Type header not set correctly")
	}

	if req.Method != "POST" {
		t.Errorf("Expected method POST, got %s", req.Method)
	}
}

func TestTaskBatchData(t *testing.T) {
	// 测试批量任务数据结构
	batchReq := map[string]interface{}{
		"projectId": "507f1f77bcf86cd799439011",
		"requests": []map[string]interface{}{
			{
				"url":    "https://example.com/file1.zip",
				"method": "GET",
				"headers": map[string]string{
					"User-Agent": "TestAgent/1.0",
				},
			},
			{
				"url":    "https://example.com/file2.zip",
				"method": "POST",
				"headers": map[string]string{
					"Content-Type": "application/json",
				},
			},
			{
				"url": "https://example.com/file3.zip",
			},
		},
		"priority": 5,
	}

	jsonData, err := json.Marshal(batchReq)
	if err != nil {
		t.Fatalf("Failed to marshal batch request: %v", err)
	}

	var decoded map[string]interface{}
	err = json.Unmarshal(jsonData, &decoded)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	requests := decoded["requests"].([]interface{})
	if len(requests) != 3 {
		t.Errorf("Expected 3 requests, got %d", len(requests))
	}

	firstRequest := requests[0].(map[string]interface{})
	if firstRequest["url"] != "https://example.com/file1.zip" {
		t.Errorf("Expected first URL 'https://example.com/file1.zip', got %v", firstRequest["url"])
	}

	if firstRequest["method"] != "GET" {
		t.Errorf("Expected first method 'GET', got %v", firstRequest["method"])
	}

	if decoded["priority"].(float64) != 5 {
		t.Errorf("Expected priority 5, got %v", decoded["priority"])
	}
}

func TestConfigurationValidation(t *testing.T) {
	// 测试配置验证
	cfg := &config.Config{
		Server: config.ServerConfig{
			Port: 8080,
			Mode: "test",
		},
		Database: config.DatabaseConfig{
			MongoDB: config.MongoDBConfig{
				URI:      "mongodb://localhost:27017",
				Database: "test_db",
				Timeout:  10 * time.Second,
			},
			Redis: config.RedisConfig{
				Addr:     "localhost:6379",
				Password: "",
				DB:       0,
			},
		},
	}

	// 基本验证
	if cfg.Server.Port <= 0 || cfg.Server.Port > 65535 {
		t.Errorf("Invalid port: %d", cfg.Server.Port)
	}

	if cfg.Database.MongoDB.URI == "" {
		t.Error("MongoDB URI cannot be empty")
	}

	if cfg.Database.Redis.Addr == "" {
		t.Error("Redis address cannot be empty")
	}

	if cfg.Database.MongoDB.Timeout <= 0 {
		t.Error("MongoDB timeout must be positive")
	}
}
