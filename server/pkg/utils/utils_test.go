package utils

import (
	"strings"
	"testing"
	"time"
)

func TestGenerateToken(t *testing.T) {
	prefix := "test"
	token := GenerateToken(prefix)

	if !strings.HasPrefix(token, prefix) {
		t.<PERSON>rrorf("Token should start with prefix '%s', got %s", prefix, token)
	}

	// Token应该是 prefix + "_" + 32位十六进制字符串
	expectedLength := len(prefix) + 1 + 32 // prefix + "_" + hex
	if len(token) != expectedLength {
		t.<PERSON>("Expected token length %d, got %d", expectedLength, len(token))
	}

	// 测试多次生成的token应该不同
	token2 := GenerateToken(prefix)
	if token == token2 {
		t.<PERSON><PERSON>r("Generated tokens should be different")
	}
}

func TestFormatDuration(t *testing.T) {
	tests := []struct {
		duration time.Duration
		expected string
	}{
		{30 * time.Second, "30s"},
		{45 * time.Second, "45s"},
		{90 * time.Second, "1.5m"},
		{2 * time.Minute, "2.0m"},
		{65 * time.Minute, "1.1h"},
		{2 * time.Hour, "2.0h"},
		{90 * time.Minute, "1.5h"},
	}

	for _, test := range tests {
		result := FormatDuration(test.duration)
		if result != test.expected {
			t.Errorf("FormatDuration(%v) = %s, expected %s", test.duration, result, test.expected)
		}
	}
}

func TestFormatBytes(t *testing.T) {
	tests := []struct {
		bytes    int64
		expected string
	}{
		{0, "0 B"},
		{512, "512 B"},
		{1023, "1023 B"},
		{1024, "1.0 KB"},
		{1536, "1.5 KB"},
		{1048576, "1.0 MB"},
		{1073741824, "1.0 GB"},
		{1099511627776, "1.0 TB"},
		{1125899906842624, "1.0 PB"},
		{1152921504606846976, "1.0 EB"},
		{2048, "2.0 KB"},
		{5368709120, "5.0 GB"},
	}

	for _, test := range tests {
		result := FormatBytes(test.bytes)
		if result != test.expected {
			t.Errorf("FormatBytes(%d) = %s, expected %s", test.bytes, result, test.expected)
		}
	}
}

func TestGenerateTokenUniqueness(t *testing.T) {
	// 测试生成大量token的唯一性
	tokens := make(map[string]bool)
	prefix := "unique"

	for i := 0; i < 1000; i++ {
		token := GenerateToken(prefix)
		if tokens[token] {
			t.Errorf("Duplicate token generated: %s", token)
		}
		tokens[token] = true
	}
}

func TestFormatBytesEdgeCases(t *testing.T) {
	// 测试边界情况
	tests := []struct {
		bytes    int64
		contains string // 只检查是否包含某个字符串
	}{
		{-1, "B"}, // 负数应该仍然能处理
		{1023, "B"},
		{1025, "KB"},
		{1048575, "KB"},
		{1048577, "MB"},
	}

	for _, test := range tests {
		result := FormatBytes(test.bytes)
		if !strings.Contains(result, test.contains) {
			t.Errorf("FormatBytes(%d) = %s, should contain %s", test.bytes, result, test.contains)
		}
	}
}

func BenchmarkGenerateToken(b *testing.B) {
	for i := 0; i < b.N; i++ {
		GenerateToken("bench")
	}
}

func BenchmarkFormatBytes(b *testing.B) {
	for i := 0; i < b.N; i++ {
		FormatBytes(1073741824) // 1GB
	}
}

func BenchmarkFormatDuration(b *testing.B) {
	duration := 90 * time.Minute
	for i := 0; i < b.N; i++ {
		FormatDuration(duration)
	}
}
