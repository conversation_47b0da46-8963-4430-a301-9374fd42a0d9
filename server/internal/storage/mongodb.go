package storage

import (
	"context"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/crypto/bcrypt"
)

type MongoDB struct {
	client   *mongo.Client
	database *mongo.Database
}

// NewMongoDB 创建MongoDB连接
func NewMongoDB(uri, database string, timeout time.Duration) (*MongoDB, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	client, err := mongo.Connect(ctx, options.Client().ApplyURI(uri))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// 测试连接
	err = client.Ping(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	db := client.Database(database)

	// 创建索引
	mongodb := &MongoDB{
		client:   client,
		database: db,
	}

	if err := mongodb.createIndexes(ctx); err != nil {
		return nil, fmt.Errorf("failed to create indexes: %w", err)
	}

	return mongodb, nil
}

// createIndexes 创建必要的索引
func (m *MongoDB) createIndexes(ctx context.Context) error {
	// Projects collection indexes
	projectsCollection := m.database.Collection("projects")
	_, err := projectsCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.D{{Key: "token", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "status", Value: 1}}},
	})
	if err != nil {
		return err
	}

	// Workers collection indexes
	workersCollection := m.database.Collection("workers")
	_, err = workersCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.D{{Key: "name", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "status", Value: 1}}},
		{Keys: bson.D{{Key: "lastHeartbeat", Value: 1}}},
	})
	if err != nil {
		return err
	}

	// Batches collection indexes
	batchesCollection := m.database.Collection("batches")
	_, err = batchesCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.D{{Key: "projectId", Value: 1}}},
		{Keys: bson.D{{Key: "status", Value: 1}}},
		{Keys: bson.D{{Key: "createdAt", Value: -1}}},
	})
	if err != nil {
		return err
	}

	// Packages collection indexes
	packagesCollection := m.database.Collection("packages")
	_, err = packagesCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.D{{Key: "projectId", Value: 1}}},
		{Keys: bson.D{{Key: "batchId", Value: 1}}},
		{Keys: bson.D{{Key: "workerId", Value: 1}}},
		{Keys: bson.D{{Key: "createdAt", Value: -1}}},
	})
	if err != nil {
		return err
	}

	// Users collection indexes
	usersCollection := m.database.Collection("users")
	_, err = usersCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.D{{Key: "username", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "email", Value: 1}}, Options: options.Index().SetSparse(true)},
		{Keys: bson.D{{Key: "status", Value: 1}}},
	})
	if err != nil {
		return err
	}

	// User sessions collection indexes
	userSessionsCollection := m.database.Collection("user_sessions")
	_, err = userSessionsCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.D{{Key: "token", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "userId", Value: 1}}},
		{Keys: bson.D{{Key: "expiresAt", Value: 1}}},
	})

	return err
}

// Close 关闭数据库连接
func (m *MongoDB) Close(ctx context.Context) error {
	return m.client.Disconnect(ctx)
}

// Project operations

// CreateProject 创建项目
func (m *MongoDB) CreateProject(ctx context.Context, project *Project) error {
	project.ID = primitive.NewObjectID()
	project.CreatedAt = time.Now()
	project.UpdatedAt = time.Now()

	// 生成token
	hash := md5.Sum([]byte(fmt.Sprintf("%s_%d", project.Name, time.Now().UnixNano())))
	project.Token = fmt.Sprintf("Bearer_%x", hash)

	collection := m.database.Collection("projects")
	_, err := collection.InsertOne(ctx, project)
	return err
}

// GetProject 根据ID获取项目
func (m *MongoDB) GetProject(ctx context.Context, id primitive.ObjectID) (*Project, error) {
	collection := m.database.Collection("projects")
	var project Project
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&project)
	if err != nil {
		return nil, err
	}
	return &project, nil
}

// GetProjectByToken 根据token获取项目
func (m *MongoDB) GetProjectByToken(ctx context.Context, token string) (*Project, error) {
	collection := m.database.Collection("projects")
	var project Project
	err := collection.FindOne(ctx, bson.M{"token": token}).Decode(&project)
	if err != nil {
		return nil, err
	}
	return &project, nil
}

// ListProjects 获取项目列表
func (m *MongoDB) ListProjects(ctx context.Context) ([]*Project, error) {
	collection := m.database.Collection("projects")
	cursor, err := collection.Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var projects []*Project
	for cursor.Next(ctx) {
		var project Project
		if err := cursor.Decode(&project); err != nil {
			return nil, err
		}
		projects = append(projects, &project)
	}
	return projects, cursor.Err()
}

// UpdateProject 更新项目
func (m *MongoDB) UpdateProject(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	collection := m.database.Collection("projects")
	update["updatedAt"] = time.Now()
	_, err := collection.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	return err
}

// DeleteProject 删除项目
func (m *MongoDB) DeleteProject(ctx context.Context, id primitive.ObjectID) error {
	collection := m.database.Collection("projects")
	_, err := collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

// Worker operations

// CreateWorker 创建Worker
func (m *MongoDB) CreateWorker(ctx context.Context, worker *Worker) error {
	worker.ID = primitive.NewObjectID()
	if worker.CreatedAt.IsZero() {
		worker.CreatedAt = time.Now()
	}

	collection := m.database.Collection("workers")
	_, err := collection.InsertOne(ctx, worker)
	return err
}

// GetWorker 根据ID获取Worker
func (m *MongoDB) GetWorker(ctx context.Context, id primitive.ObjectID) (*Worker, error) {
	collection := m.database.Collection("workers")
	var worker Worker
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&worker)
	if err != nil {
		return nil, err
	}
	return &worker, nil
}

// GetWorkerByToken 根据token获取Worker
func (m *MongoDB) GetWorkerByToken(ctx context.Context, token string) (*Worker, error) {
	collection := m.database.Collection("workers")
	var worker Worker
	err := collection.FindOne(ctx, bson.M{"token": token}).Decode(&worker)
	if err != nil {
		return nil, err
	}
	return &worker, nil
}

// ListWorkers 获取Worker列表
func (m *MongoDB) ListWorkers(ctx context.Context) ([]*Worker, error) {
	collection := m.database.Collection("workers")
	cursor, err := collection.Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var workers []*Worker
	for cursor.Next(ctx) {
		var worker Worker
		if err := cursor.Decode(&worker); err != nil {
			return nil, err
		}
		workers = append(workers, &worker)
	}
	return workers, cursor.Err()
}

// UpdateWorker 更新Worker
func (m *MongoDB) UpdateWorker(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	collection := m.database.Collection("workers")
	_, err := collection.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	return err
}

// UpdateWorkerHeartbeat 更新Worker心跳
func (m *MongoDB) UpdateWorkerHeartbeat(ctx context.Context, token string, stats *WorkerStats, health *WorkerHealth) error {
	collection := m.database.Collection("workers")
	now := time.Now()
	update := bson.M{
		"lastHeartbeat": &now,
		"status":        "active",
	}

	if stats != nil {
		update["stats"] = stats
	}

	if health != nil {
		update["health"] = health
	}

	_, err := collection.UpdateOne(ctx, bson.M{"token": token}, bson.M{"$set": update})
	return err
}

// DeleteWorker 删除Worker
func (m *MongoDB) DeleteWorker(ctx context.Context, id primitive.ObjectID) error {
	collection := m.database.Collection("workers")
	_, err := collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

// Batch operations

// CreateBatch 创建批次
func (m *MongoDB) CreateBatch(ctx context.Context, batch *Batch) error {
	batch.ID = primitive.NewObjectID()
	batch.CreatedAt = time.Now()
	batch.UpdatedAt = time.Now()

	collection := m.database.Collection("batches")
	_, err := collection.InsertOne(ctx, batch)
	return err
}

// GetBatch 根据ID获取批次
func (m *MongoDB) GetBatch(ctx context.Context, id primitive.ObjectID) (*Batch, error) {
	collection := m.database.Collection("batches")
	var batch Batch
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&batch)
	if err != nil {
		return nil, err
	}
	return &batch, nil
}

// UpdateBatch 更新批次
func (m *MongoDB) UpdateBatch(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	collection := m.database.Collection("batches")
	update["updatedAt"] = time.Now()
	_, err := collection.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	return err
}

// Package operations

// CreatePackage 创建数据包记录
func (m *MongoDB) CreatePackage(ctx context.Context, pkg *Package) error {
	pkg.ID = primitive.NewObjectID()
	pkg.CreatedAt = time.Now()

	collection := m.database.Collection("packages")
	_, err := collection.InsertOne(ctx, pkg)
	return err
}

// ListPackages 获取数据包列表
func (m *MongoDB) ListPackages(ctx context.Context, projectID primitive.ObjectID) ([]*Package, error) {
	collection := m.database.Collection("packages")
	filter := bson.M{"projectId": projectID}
	cursor, err := collection.Find(ctx, filter, options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}}))
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var packages []*Package
	for cursor.Next(ctx) {
		var pkg Package
		if err := cursor.Decode(&pkg); err != nil {
			return nil, err
		}
		packages = append(packages, &pkg)
	}
	return packages, cursor.Err()
}

// User operations

// CreateUser 创建用户
func (m *MongoDB) CreateUser(ctx context.Context, user *User) error {
	user.ID = primitive.NewObjectID()
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}
	user.Password = string(hashedPassword)

	collection := m.database.Collection("users")
	_, err = collection.InsertOne(ctx, user)
	return err
}

// GetUserByUsername 根据用户名获取用户
func (m *MongoDB) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	collection := m.database.Collection("users")
	var user User
	err := collection.FindOne(ctx, bson.M{"username": username}).Decode(&user)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByID 根据ID获取用户
func (m *MongoDB) GetUserByID(ctx context.Context, id primitive.ObjectID) (*User, error) {
	collection := m.database.Collection("users")
	var user User
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&user)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// VerifyUserPassword 验证用户密码
func (m *MongoDB) VerifyUserPassword(ctx context.Context, username, password string) (*User, error) {
	user, err := m.GetUserByUsername(ctx, username)
	if err != nil {
		return nil, err
	}

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		return nil, fmt.Errorf("invalid password")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	collection := m.database.Collection("users")
	_, err = collection.UpdateOne(ctx, bson.M{"_id": user.ID}, bson.M{
		"$set": bson.M{"lastLoginAt": now},
	})
	if err != nil {
		return nil, err
	}

	return user, nil
}

// UpdateUser 更新用户信息
func (m *MongoDB) UpdateUser(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	collection := m.database.Collection("users")
	update["updatedAt"] = time.Now()
	_, err := collection.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	return err
}

// CreateUserSession 创建用户会话
func (m *MongoDB) CreateUserSession(ctx context.Context, userID primitive.ObjectID, userAgent, ipAddress string) (*UserSession, error) {
	// 生成安全的随机token
	token, err := m.generateSecureToken()
	if err != nil {
		return nil, err
	}

	session := &UserSession{
		ID:        primitive.NewObjectID(),
		UserID:    userID,
		Token:     token,
		UserAgent: userAgent,
		IPAddress: ipAddress,
		ExpiresAt: time.Now().Add(24 * time.Hour), // 24小时过期
		CreatedAt: time.Now(),
	}

	collection := m.database.Collection("user_sessions")
	_, err = collection.InsertOne(ctx, session)
	if err != nil {
		return nil, err
	}

	return session, nil
}

// GetUserByToken 根据token获取用户信息
func (m *MongoDB) GetUserByToken(ctx context.Context, token string) (*User, error) {
	// 首先查找会话
	sessionCollection := m.database.Collection("user_sessions")
	var session UserSession
	err := sessionCollection.FindOne(ctx, bson.M{
		"token":     token,
		"expiresAt": bson.M{"$gt": time.Now()}, // 检查会话是否过期
	}).Decode(&session)
	if err != nil {
		return nil, err
	}

	// 根据用户ID获取用户信息
	return m.GetUserByID(ctx, session.UserID)
}

// DeleteUserSession 删除用户会话
func (m *MongoDB) DeleteUserSession(ctx context.Context, token string) error {
	collection := m.database.Collection("user_sessions")
	_, err := collection.DeleteOne(ctx, bson.M{"token": token})
	return err
}

// DeleteUserSessionsByUserID 删除用户的所有会话
func (m *MongoDB) DeleteUserSessionsByUserID(ctx context.Context, userID primitive.ObjectID) error {
	collection := m.database.Collection("user_sessions")
	_, err := collection.DeleteMany(ctx, bson.M{"userId": userID})
	return err
}

// CleanExpiredSessions 清理过期的会话
func (m *MongoDB) CleanExpiredSessions(ctx context.Context) error {
	collection := m.database.Collection("user_sessions")
	_, err := collection.DeleteMany(ctx, bson.M{
		"expiresAt": bson.M{"$lt": time.Now()},
	})
	return err
}

// generateSecureToken 生成安全的随机token
func (m *MongoDB) generateSecureToken() (string, error) {
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}

	// 使用SHA256生成token
	hash := sha256.Sum256(bytes)
	return hex.EncodeToString(hash[:]), nil
}

// ListUsers 获取用户列表
func (m *MongoDB) ListUsers(ctx context.Context) ([]*User, error) {
	collection := m.database.Collection("users")
	cursor, err := collection.Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var users []*User
	for cursor.Next(ctx) {
		var user User
		if err := cursor.Decode(&user); err != nil {
			return nil, err
		}
		users = append(users, &user)
	}
	return users, cursor.Err()
}

// DeleteUser 删除用户
func (m *MongoDB) DeleteUser(ctx context.Context, id primitive.ObjectID) error {
	// 先删除用户的所有会话
	err := m.DeleteUserSessionsByUserID(ctx, id)
	if err != nil {
		return err
	}

	// 删除用户
	collection := m.database.Collection("users")
	_, err = collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}
