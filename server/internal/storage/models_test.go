package storage

import (
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestProjectModel(t *testing.T) {
	project := &Project{
		Name:   "Test Project",
		Status: "active",
		Config: ProjectConfig{
			PackSizeGB:      10,
			Concurrent:      5,
			RetryTimes:      3,
			DownloadTimeout: 300,
			OSSConfig: OSSConfig{
				Provider:       "aliyun",
				Endpoint:       "oss-cn-beijing.aliyuncs.com",
				Bucket:         "test-bucket",
				AccessKey:      "test-key",
				SecretKey:      "test-secret",
				Prefix:         "test/",
				Region:         "cn-beijing",
				ForcePathStyle: false,
			},
		},
	}

	if project.Name != "Test Project" {
		t.Errorf("Expected project name 'Test Project', got %s", project.Name)
	}

	if project.Config.PackSizeGB != 10 {
		t.<PERSON><PERSON><PERSON>("Expected pack size 10GB, got %d", project.Config.PackSizeGB)
	}

	if project.Config.OSSConfig.Provider != "aliyun" {
		t.<PERSON><PERSON><PERSON>("Expected provider 'aliyun', got %s", project.Config.OSSConfig.Provider)
	}
}

func TestWorkerModel(t *testing.T) {
	worker := &Worker{
		Name:   "test-worker",
		Host:   "*************",
		Port:   8081,
		Status: "active",
		Capabilities: WorkerCapabilities{
			MaxConcurrent: 10,
			DiskSpace:     500,
			Bandwidth:     1000,
		},
		Stats: WorkerStats{
			CPUUsage:       45.5,
			MemoryUsage:    60.2,
			DiskUsage:      30.5,
			ActiveTasks:    3,
			TotalProcessed: 1000,
			TotalFailed:    10,
		},
	}

	if worker.Name != "test-worker" {
		t.Errorf("Expected worker name 'test-worker', got %s", worker.Name)
	}

	if worker.Capabilities.MaxConcurrent != 10 {
		t.Errorf("Expected max concurrent 10, got %d", worker.Capabilities.MaxConcurrent)
	}

	if worker.Stats.CPUUsage != 45.5 {
		t.Errorf("Expected CPU usage 45.5, got %f", worker.Stats.CPUUsage)
	}
}

func TestBatchModel(t *testing.T) {
	projectID := primitive.NewObjectID()
	batch := &Batch{
		ProjectID:  projectID,
		BatchNo:    1,
		TotalCount: 1000,
		Status:     "processing",
		Stats: BatchStats{
			Pending:     800,
			Downloading: 50,
			Completed:   140,
			Failed:      10,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if batch.ProjectID != projectID {
		t.Errorf("Project ID mismatch")
	}

	if batch.TotalCount != 1000 {
		t.Errorf("Expected total count 1000, got %d", batch.TotalCount)
	}

	if batch.Stats.Pending != 800 {
		t.Errorf("Expected pending 800, got %d", batch.Stats.Pending)
	}

	// 验证统计数据一致性
	total := batch.Stats.Pending + batch.Stats.Downloading + batch.Stats.Completed + batch.Stats.Failed
	if total != batch.TotalCount {
		t.Errorf("Stats don't add up: %d != %d", total, batch.TotalCount)
	}
}

func TestPackageModel(t *testing.T) {
	projectID := primitive.NewObjectID()
	batchID := primitive.NewObjectID()
	workerID := primitive.NewObjectID()

	pkg := &Package{
		ProjectID:   projectID,
		BatchID:     batchID,
		WorkerID:    workerID,
		OSSPath:     "project1/2024-01-15/package_test.tar.gz",
		Size:        10737418240, // 10GB
		FileCount:   5000,
		Checksum:    "test_md5_hash",
		UploadSpeed: 104857600, // 100MB/s
		CreatedAt:   time.Now(),
		UploadedAt:  time.Now(),
	}

	if pkg.ProjectID != projectID {
		t.Errorf("Project ID mismatch")
	}

	if pkg.Size != 10737418240 {
		t.Errorf("Expected size 10737418240, got %d", pkg.Size)
	}

	if pkg.FileCount != 5000 {
		t.Errorf("Expected file count 5000, got %d", pkg.FileCount)
	}
}

func TestProjectStats(t *testing.T) {
	stats := &ProjectStats{
		TotalTasks:       1000000,
		PendingTasks:     800000,
		DownloadingTasks: 50000,
		CompletedTasks:   140000,
		FailedTasks:      10000,
		TotalSize:        1099511627776, // 1TB
		DownloadSpeed:    104857600,     // 100MB/s
		LastUpdate:       time.Now().Unix(),
	}

	// 计算成功率
	if stats.TotalTasks > 0 {
		stats.SuccessRate = float64(stats.CompletedTasks) / float64(stats.TotalTasks) * 100
	}

	expectedSuccessRate := float64(140000) / float64(1000000) * 100
	if stats.SuccessRate != expectedSuccessRate {
		t.Errorf("Expected success rate %f, got %f", expectedSuccessRate, stats.SuccessRate)
	}

	// 验证任务总数
	total := stats.PendingTasks + stats.DownloadingTasks + stats.CompletedTasks + stats.FailedTasks
	if total != stats.TotalTasks {
		t.Errorf("Task counts don't add up: %d != %d", total, stats.TotalTasks)
	}
}
