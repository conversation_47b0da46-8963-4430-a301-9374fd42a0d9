package storage

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// User corresponds to the "users" collection in MongoDB.
type User struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Username    string             `bson:"username" json:"username"`
	Email       string             `bson:"email,omitempty" json:"email,omitempty"`
	Password    string             `bson:"password" json:"-"` // 不在JSON中返回密码
	Name        string             `bson:"name" json:"name"`
	Role        string             `bson:"role" json:"role"`     // admin|operator|viewer
	Status      string             `bson:"status" json:"status"` // active|inactive|disabled
	Avatar      string             `bson:"avatar,omitempty" json:"avatar,omitempty"`
	LastLoginAt *time.Time         `bson:"lastLoginAt,omitempty" json:"lastLoginAt,omitempty"`
	CreatedAt   time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt   time.Time          `bson:"updatedAt" json:"updatedAt"`
}

// UserSession corresponds to the "user_sessions" collection in MongoDB.
type UserSession struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	UserID    primitive.ObjectID `bson:"userId" json:"userId"`
	Token     string             `bson:"token" json:"token"`
	UserAgent string             `bson:"userAgent,omitempty" json:"userAgent,omitempty"`
	IPAddress string             `bson:"ipAddress,omitempty" json:"ipAddress,omitempty"`
	ExpiresAt time.Time          `bson:"expiresAt" json:"expiresAt"`
	CreatedAt time.Time          `bson:"createdAt" json:"createdAt"`
}

// LoginRequest 登录请求结构体
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构体
type LoginResponse struct {
	Token     string `json:"token"`
	User      *User  `json:"user"`
	ExpiresAt int64  `json:"expiresAt"`
}

// ChangePasswordRequest 修改密码请求结构体
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required"`
}

// CreateUserRequest 创建用户请求结构体
type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Name     string `json:"name" binding:"required"`
	Email    string `json:"email,omitempty"`
	Role     string `json:"role" binding:"required"`
}

// Project corresponds to the "projects" collection in MongoDB.
type Project struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Name      string             `bson:"name" json:"name"`
	Token     string             `bson:"token" json:"token"`
	Config    ProjectConfig      `bson:"config" json:"config"`
	Status    string             `bson:"status" json:"status"` // active|paused|completed
	CreatedAt time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt time.Time          `bson:"updatedAt" json:"updatedAt"`
}

type ProjectConfig struct {
	PackSizeGB      int       `bson:"packSizeGB" json:"packSizeGB"`
	OSSConfig       OSSConfig `bson:"ossConfig" json:"ossConfig"`
	Concurrent      int       `bson:"concurrent" json:"concurrent"`
	RetryTimes      int       `bson:"retryTimes" json:"retryTimes"`
	DownloadTimeout int       `bson:"downloadTimeout" json:"downloadTimeout"`
}

type OSSConfig struct {
	Provider       string `bson:"provider" json:"provider"` // aliyun|aws|minio
	Endpoint       string `bson:"endpoint" json:"endpoint"`
	Bucket         string `bson:"bucket" json:"bucket"`
	AccessKey      string `bson:"accessKey" json:"accessKey"`
	SecretKey      string `bson:"secretKey" json:"secretKey"`
	Prefix         string `bson:"prefix" json:"prefix"`
	Region         string `bson:"region" json:"region"`
	ForcePathStyle bool   `bson:"forcePathStyle" json:"forcePathStyle"` // 强制使用路径样式，MinIO需要此选项
}

// Worker corresponds to the "workers" collection in MongoDB.
type Worker struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Name        string             `bson:"name" json:"name"`
	Description string             `bson:"description" json:"description"`
	Status      string             `bson:"status" json:"status"` // pending|active|offline|error|disabled

	// 预注册信息
	Token        string             `bson:"token" json:"token,omitempty"`
	Capabilities WorkerCapabilities `bson:"capabilities" json:"capabilities"`

	// 激活后信息
	Host       string            `bson:"host,omitempty" json:"host,omitempty"`
	Port       int               `bson:"port,omitempty" json:"port,omitempty"`
	Version    string            `bson:"version,omitempty" json:"version,omitempty"`
	SystemInfo *WorkerSystemInfo `bson:"systemInfo,omitempty" json:"systemInfo,omitempty"`

	// 运行时信息
	Stats    *WorkerStats  `bson:"stats,omitempty" json:"stats,omitempty"`
	Health   *WorkerHealth `bson:"health,omitempty" json:"health,omitempty"`
	Projects []string      `bson:"projects" json:"projects"`

	// 时间信息
	CreatedAt     time.Time  `bson:"createdAt" json:"createdAt"`
	ActivatedAt   *time.Time `bson:"activatedAt,omitempty" json:"activatedAt,omitempty"`
	LastHeartbeat *time.Time `bson:"lastHeartbeat,omitempty" json:"lastHeartbeat,omitempty"`
}

type WorkerCapabilities struct {
	MaxConcurrent int `bson:"maxConcurrent" json:"maxConcurrent"`
	DiskSpace     int `bson:"diskSpace" json:"diskSpace"` // GB
	Bandwidth     int `bson:"bandwidth" json:"bandwidth"` // Mbps
}

type WorkerSystemInfo struct {
	OS          string `bson:"os" json:"os"`
	Arch        string `bson:"arch" json:"arch"`
	CPUCores    int    `bson:"cpuCores" json:"cpuCores"`
	TotalMemory int64  `bson:"totalMemory" json:"totalMemory"` // MB
	TotalDisk   int64  `bson:"totalDisk" json:"totalDisk"`     // GB
}

type WorkerStats struct {
	CPUUsage       float64 `bson:"cpuUsage" json:"cpuUsage"`
	MemoryUsage    float64 `bson:"memoryUsage" json:"memoryUsage"`
	DiskUsage      float64 `bson:"diskUsage" json:"diskUsage"`
	ActiveTasks    int     `bson:"activeTasks" json:"activeTasks"`
	TotalProcessed int64   `bson:"totalProcessed" json:"totalProcessed"`
	TotalFailed    int64   `bson:"totalFailed" json:"totalFailed"`
	NetworkSpeed   int64   `bson:"networkSpeed" json:"networkSpeed"` // 字节/秒
}

type WorkerHealth struct {
	Status    string `bson:"status" json:"status"` // healthy|warning|error
	Uptime    int64  `bson:"uptime" json:"uptime"` // 秒
	LastError string `bson:"lastError,omitempty" json:"lastError,omitempty"`
}

// Batch corresponds to the "batches" collection in MongoDB.
type Batch struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProjectID   primitive.ObjectID `bson:"projectId" json:"projectId"`
	BatchNo     int                `bson:"batchNo" json:"batchNo"`
	TotalCount  int                `bson:"totalCount" json:"totalCount"`
	Status      string             `bson:"status" json:"status"` // pending|processing|completed
	Stats       BatchStats         `bson:"stats" json:"stats"`
	CreatedAt   time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt   time.Time          `bson:"updatedAt" json:"updatedAt"`
	CompletedAt *time.Time         `bson:"completedAt,omitempty" json:"completedAt,omitempty"`
}

type BatchStats struct {
	Pending     int `bson:"pending" json:"pending"`
	Downloading int `bson:"downloading" json:"downloading"`
	Completed   int `bson:"completed" json:"completed"`
	Failed      int `bson:"failed" json:"failed"`
}

// Package corresponds to the "packages" collection in MongoDB.
type Package struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProjectID   primitive.ObjectID `bson:"projectId" json:"projectId"`
	BatchID     primitive.ObjectID `bson:"batchId" json:"batchId"`
	WorkerID    primitive.ObjectID `bson:"workerId" json:"workerId"`
	OSSPath     string             `bson:"ossPath" json:"ossPath"`
	Size        int64              `bson:"size" json:"size"`
	FileCount   int                `bson:"fileCount" json:"fileCount"`
	Checksum    string             `bson:"checksum" json:"checksum"`
	UploadSpeed int64              `bson:"uploadSpeed" json:"uploadSpeed"` // 字节/秒
	CreatedAt   time.Time          `bson:"createdAt" json:"createdAt"`
	UploadedAt  time.Time          `bson:"uploadedAt" json:"uploadedAt"`
}

// ProjectStats Redis中的项目统计信息
type ProjectStats struct {
	TotalTasks       int64   `json:"totalTasks"`
	PendingTasks     int64   `json:"pendingTasks"`
	DownloadingTasks int64   `json:"downloadingTasks"`
	CompletedTasks   int64   `json:"completedTasks"`
	FailedTasks      int64   `json:"failedTasks"`
	TotalSize        int64   `json:"totalSize"`
	DownloadSpeed    int64   `json:"downloadSpeed"`
	SuccessRate      float64 `json:"successRate"`
	LastUpdate       int64   `json:"lastUpdate"`
}

// TaskMessage 消息队列中的任务消息格式
type TaskMessage struct {
	ID         string            `json:"id"`
	ProjectID  string            `json:"projectId"`
	BatchID    string            `json:"batchId"`
	URL        string            `json:"url"`
	Method     string            `json:"method,omitempty"`
	Headers    map[string]string `json:"headers,omitempty"`
	Priority   int               `json:"priority"`
	RetryCount int               `json:"retryCount"`
	OSSConfig  OSSConfig         `json:"ossConfig"`
}
