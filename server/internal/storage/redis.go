package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisClient struct {
	client *redis.Client
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(addr, password string, db int) (*RedisClient, error) {
	log.Println("NewRedisClient", addr, password, db)
	rdb := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &RedisClient{client: rdb}, nil
}

// Close 关闭Redis连接
func (r *RedisClient) Close() error {
	return r.client.Close()
}

// Project Stats operations

// SetProjectStats 设置项目统计信息
func (r *RedisClient) SetProjectStats(ctx context.Context, projectID string, stats *ProjectStats) error {
	key := fmt.Sprintf("project:stats:%s", projectID)
	pipe := r.client.Pipeline()

	pipe.HSet(ctx, key, map[string]interface{}{
		"total_tasks":       stats.TotalTasks,
		"pending_tasks":     stats.PendingTasks,
		"downloading_tasks": stats.DownloadingTasks,
		"completed_tasks":   stats.CompletedTasks,
		"failed_tasks":      stats.FailedTasks,
		"total_size":        stats.TotalSize,
		"download_speed":    stats.DownloadSpeed,
		"success_rate":      stats.SuccessRate,
		"last_update":       stats.LastUpdate,
	})
	pipe.Expire(ctx, key, 7*24*time.Hour) // 7天过期

	_, err := pipe.Exec(ctx)
	return err
}

// GetProjectStats 获取项目统计信息
func (r *RedisClient) GetProjectStats(ctx context.Context, projectID string) (*ProjectStats, error) {
	key := fmt.Sprintf("project:stats:%s", projectID)
	result, err := r.client.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	if len(result) == 0 {
		return &ProjectStats{}, nil
	}

	stats := &ProjectStats{}
	if val, ok := result["total_tasks"]; ok {
		stats.TotalTasks, _ = strconv.ParseInt(val, 10, 64)
	}
	if val, ok := result["pending_tasks"]; ok {
		stats.PendingTasks, _ = strconv.ParseInt(val, 10, 64)
	}
	if val, ok := result["downloading_tasks"]; ok {
		stats.DownloadingTasks, _ = strconv.ParseInt(val, 10, 64)
	}
	if val, ok := result["completed_tasks"]; ok {
		stats.CompletedTasks, _ = strconv.ParseInt(val, 10, 64)
	}
	if val, ok := result["failed_tasks"]; ok {
		stats.FailedTasks, _ = strconv.ParseInt(val, 10, 64)
	}
	if val, ok := result["total_size"]; ok {
		stats.TotalSize, _ = strconv.ParseInt(val, 10, 64)
	}
	if val, ok := result["download_speed"]; ok {
		stats.DownloadSpeed, _ = strconv.ParseInt(val, 10, 64)
	}
	if val, ok := result["success_rate"]; ok {
		stats.SuccessRate, _ = strconv.ParseFloat(val, 64)
	}
	if val, ok := result["last_update"]; ok {
		stats.LastUpdate, _ = strconv.ParseInt(val, 10, 64)
	}

	return stats, nil
}

// UpdateProjectStats 增量更新项目统计
func (r *RedisClient) UpdateProjectStats(ctx context.Context, projectID string, updates map[string]interface{}) error {
	key := fmt.Sprintf("project:stats:%s", projectID)
	updates["last_update"] = time.Now().Unix()

	pipe := r.client.Pipeline()
	for field, value := range updates {
		pipe.HSet(ctx, key, field, value)
	}
	pipe.Expire(ctx, key, 7*24*time.Hour)

	_, err := pipe.Exec(ctx)
	return err
}

// IncrementProjectStats 增量更新项目统计计数器
func (r *RedisClient) IncrementProjectStats(ctx context.Context, projectID string, field string, increment int64) error {
	key := fmt.Sprintf("project:stats:%s", projectID)
	pipe := r.client.Pipeline()
	pipe.HIncrBy(ctx, key, field, increment)
	pipe.HSet(ctx, key, "last_update", time.Now().Unix())
	pipe.Expire(ctx, key, 7*24*time.Hour)

	_, err := pipe.Exec(ctx)
	return err
}

// Worker Stats operations

// SetWorkerStats 设置Worker统计信息
func (r *RedisClient) SetWorkerStats(ctx context.Context, workerID string, stats map[string]interface{}) error {
	key := fmt.Sprintf("worker:stats:%s", workerID)
	stats["last_heartbeat"] = time.Now().Unix()

	pipe := r.client.Pipeline()
	pipe.HMSet(ctx, key, stats)
	pipe.Expire(ctx, key, 1*time.Hour) // 1小时过期

	_, err := pipe.Exec(ctx)
	return err
}

// GetWorkerStats 获取Worker统计信息
func (r *RedisClient) GetWorkerStats(ctx context.Context, workerID string) (map[string]string, error) {
	key := fmt.Sprintf("worker:stats:%s", workerID)
	return r.client.HGetAll(ctx, key).Result()
}

// GetAllWorkerStats 获取所有Worker统计信息
func (r *RedisClient) GetAllWorkerStats(ctx context.Context) (map[string]map[string]string, error) {
	pattern := "worker:stats:*"
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, err
	}

	result := make(map[string]map[string]string)
	for _, key := range keys {
		stats, err := r.client.HGetAll(ctx, key).Result()
		if err != nil {
			continue
		}
		// 从key中提取workerID
		workerID := key[len("worker:stats:"):]
		result[workerID] = stats
	}

	return result, nil
}

// Project Control operations

// SetProjectControl 设置项目控制状态
func (r *RedisClient) SetProjectControl(ctx context.Context, projectID, status string) error {
	key := fmt.Sprintf("project:control:%s", projectID)
	return r.client.Set(ctx, key, status, 24*time.Hour).Err()
}

// GetProjectControl 获取项目控制状态
func (r *RedisClient) GetProjectControl(ctx context.Context, projectID string) (string, error) {
	key := fmt.Sprintf("project:control:%s", projectID)
	result, err := r.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return "active", nil // 默认状态
	}
	return result, err
}

// Queue operations

// SetQueueDepth 设置队列深度
func (r *RedisClient) SetQueueDepth(ctx context.Context, projectID string, depth int64) error {
	key := fmt.Sprintf("queue:depth:%s", projectID)
	return r.client.Set(ctx, key, depth, 1*time.Hour).Err()
}

// GetQueueDepth 获取队列深度
func (r *RedisClient) GetQueueDepth(ctx context.Context, projectID string) (int64, error) {
	key := fmt.Sprintf("queue:depth:%s", projectID)
	result, err := r.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}
	return strconv.ParseInt(result, 10, 64)
}

// Speed History operations

// AddSpeedHistory 添加速度历史记录
func (r *RedisClient) AddSpeedHistory(ctx context.Context, projectID string, speed int64) error {
	key := fmt.Sprintf("project:speed:%s", projectID)
	timestamp := time.Now().Unix()

	pipe := r.client.Pipeline()
	pipe.ZAdd(ctx, key, redis.Z{Score: float64(timestamp), Member: speed})
	// 只保留最近24小时的数据
	pipe.ZRemRangeByScore(ctx, key, "0", strconv.FormatInt(timestamp-24*3600, 10))
	pipe.Expire(ctx, key, 25*time.Hour) // 25小时过期

	_, err := pipe.Exec(ctx)
	return err
}

// GetSpeedHistory 获取速度历史记录
func (r *RedisClient) GetSpeedHistory(ctx context.Context, projectID string, hours int) ([]map[string]interface{}, error) {
	key := fmt.Sprintf("project:speed:%s", projectID)

	// 获取指定小时数内的数据
	endTime := time.Now().Unix()
	startTime := endTime - int64(hours*3600)

	result, err := r.client.ZRangeByScoreWithScores(ctx, key, &redis.ZRangeBy{
		Min: strconv.FormatInt(startTime, 10),
		Max: strconv.FormatInt(endTime, 10),
	}).Result()

	if err != nil {
		return nil, err
	}

	history := make([]map[string]interface{}, len(result))
	for i, z := range result {
		speed, _ := strconv.ParseInt(z.Member.(string), 10, 64)
		history[i] = map[string]interface{}{
			"timestamp": int64(z.Score),
			"speed":     speed,
		}
	}

	return history, nil
}

// Monitoring operations

// SetMonitoringData 设置监控数据
func (r *RedisClient) SetMonitoringData(ctx context.Context, key string, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return r.client.Set(ctx, key, jsonData, 10*time.Minute).Err()
}

// GetMonitoringData 获取监控数据
func (r *RedisClient) GetMonitoringData(ctx context.Context, key string, target interface{}) error {
	result, err := r.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(result), target)
}

// Cache operations

// Set 设置缓存
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return r.client.Set(ctx, key, value, expiration).Err()
}

// Get 获取缓存
func (r *RedisClient) Get(ctx context.Context, key string) (string, error) {
	return r.client.Get(ctx, key).Result()
}

// Delete 删除缓存
func (r *RedisClient) Delete(ctx context.Context, keys ...string) error {
	return r.client.Del(ctx, keys...).Err()
}

// Exists 检查key是否存在
func (r *RedisClient) Exists(ctx context.Context, keys ...string) (int64, error) {
	return r.client.Exists(ctx, keys...).Result()
}
