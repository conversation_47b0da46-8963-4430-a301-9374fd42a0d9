package monitor

import (
	"context"
	"download-scheduler/server/internal/config"
	"download-scheduler/server/internal/storage"
	"log"
	"sync"
	"time"
)

// Monitor 监控管理器
type Monitor struct {
	mongoDB     *storage.MongoDB
	redisClient *storage.RedisClient
	config      *config.Config
	stop        chan struct{}
	wg          sync.WaitGroup
}

// NewMonitor 创建监控管理器
func NewMonitor(mongoDB *storage.MongoDB, redisClient *storage.RedisClient, cfg *config.Config) *Monitor {
	return &Monitor{
		mongoDB:     mongoDB,
		redisClient: redisClient,
		config:      cfg,
		stop:        make(chan struct{}),
	}
}

// Start 启动监控
func (m *Monitor) Start() {
	if !m.config.Monitoring.Enable {
		log.Println("Monitoring is disabled")
		return
	}

	log.Println("Starting monitoring services...")

	// 启动统计收集器
	m.wg.Add(1)
	go m.runStatsCollector()

	// Worker状态检查器已移除，现在基于WebSocket连接状态实时计算

	// 启动数据清理器
	m.wg.Add(1)
	go m.runDataCleaner()

	log.Println("Monitoring services started")
}

// Stop 停止监控
func (m *Monitor) Stop() {
	log.Println("Stopping monitoring services...")
	close(m.stop)
	m.wg.Wait()
	log.Println("Monitoring services stopped")
}

// runStatsCollector 运行统计收集器
func (m *Monitor) runStatsCollector() {
	defer m.wg.Done()

	ticker := time.NewTicker(m.config.Monitoring.StatsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.collectProjectStats()
		case <-m.stop:
			return
		}
	}
}

// collectProjectStats 收集项目统计信息
func (m *Monitor) collectProjectStats() {
	ctx := context.Background()

	// 获取所有活跃项目
	projects, err := m.mongoDB.ListProjects(ctx)
	if err != nil {
		log.Printf("Failed to list projects for stats collection: %s", err)
		return
	}

	for _, project := range projects {
		if project.Status != "active" {
			continue
		}

		projectID := project.ID.Hex()

		// 获取当前统计信息
		stats, err := m.redisClient.GetProjectStats(ctx, projectID)
		if err != nil {
			log.Printf("Failed to get project stats for %s: %s", projectID, err)
			continue
		}

		// 获取队列深度（这里需要实际的队列监控逻辑）
		// queueDepth := m.getQueueDepth(projectID)
		// m.redisClient.SetQueueDepth(ctx, projectID, queueDepth)

		// 记录下载速度历史
		if stats.DownloadSpeed > 0 {
			err = m.redisClient.AddSpeedHistory(ctx, projectID, stats.DownloadSpeed)
			if err != nil {
				log.Printf("Failed to add speed history for %s: %s", projectID, err)
			}
		}

		// 计算成功率
		if stats.TotalTasks > 0 {
			stats.SuccessRate = float64(stats.CompletedTasks) / float64(stats.TotalTasks) * 100
		}

		// 更新统计信息
		stats.LastUpdate = time.Now().Unix()
		err = m.redisClient.SetProjectStats(ctx, projectID, stats)
		if err != nil {
			log.Printf("Failed to update project stats for %s: %s", projectID, err)
		}
	}
}

// 旧的Worker状态检查器已移除，现在使用WebSocket连接状态实时计算Worker状态

// runDataCleaner 运行数据清理器
func (m *Monitor) runDataCleaner() {
	defer m.wg.Done()

	ticker := time.NewTicker(1 * time.Hour) // 每小时清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.cleanOldData()
		case <-m.stop:
			return
		}
	}
}

// cleanOldData 清理旧数据
func (m *Monitor) cleanOldData() {
	ctx := context.Background()

	// 清理7天前的速度历史数据
	projects, err := m.mongoDB.ListProjects(ctx)
	if err != nil {
		log.Printf("Failed to list projects for data cleanup: %s", err)
		return
	}

	for _, project := range projects {
		projectID := project.ID.Hex()

		// 清理速度历史数据（Redis中的数据会自动过期，这里主要是备份清理）
		_, err = m.redisClient.GetSpeedHistory(ctx, projectID, 1) // 触发清理
		if err != nil {
			log.Printf("Failed to clean speed history for %s: %s", projectID, err)
		}
	}

	log.Println("Data cleanup completed")
}

// GetProjectMonitoringData 获取项目监控数据
func (m *Monitor) GetProjectMonitoringData(projectID string) (*ProjectMonitoringData, error) {
	ctx := context.Background()

	// 获取项目统计
	stats, err := m.redisClient.GetProjectStats(ctx, projectID)
	if err != nil {
		return nil, err
	}

	// 获取Worker列表
	workers, err := m.mongoDB.ListWorkers(ctx)
	if err != nil {
		return nil, err
	}

	// 获取队列深度
	queueDepth, err := m.redisClient.GetQueueDepth(ctx, projectID)
	if err != nil {
		queueDepth = 0
	}

	// 获取速度历史
	speedHistory, err := m.redisClient.GetSpeedHistory(ctx, projectID, 24)
	if err != nil {
		speedHistory = []map[string]interface{}{}
	}

	// 获取Worker实时统计
	workerStats, err := m.redisClient.GetAllWorkerStats(ctx)
	if err != nil {
		workerStats = make(map[string]map[string]string)
	}

	return &ProjectMonitoringData{
		ProjectID:    projectID,
		Stats:        stats,
		Workers:      workers,
		QueueDepth:   queueDepth,
		SpeedHistory: speedHistory,
		WorkerStats:  workerStats,
		Timestamp:    time.Now().Unix(),
	}, nil
}

// ProjectMonitoringData 项目监控数据
type ProjectMonitoringData struct {
	ProjectID    string                       `json:"projectId"`
	Stats        *storage.ProjectStats        `json:"stats"`
	Workers      []*storage.Worker            `json:"workers"`
	QueueDepth   int64                        `json:"queueDepth"`
	SpeedHistory []map[string]interface{}     `json:"speedHistory"`
	WorkerStats  map[string]map[string]string `json:"workerStats"`
	Timestamp    int64                        `json:"timestamp"`
}

// UpdateTaskStats 更新任务统计（供Worker调用）
func (m *Monitor) UpdateTaskStats(projectID string, taskType TaskType, count int64) error {
	ctx := context.Background()

	var field string
	switch taskType {
	case TaskTypePending:
		field = "pending_tasks"
	case TaskTypeDownloading:
		field = "downloading_tasks"
	case TaskTypeCompleted:
		field = "completed_tasks"
	case TaskTypeFailed:
		field = "failed_tasks"
	default:
		return nil
	}

	return m.redisClient.IncrementProjectStats(ctx, projectID, field, count)
}

// TaskType 任务类型
type TaskType int

const (
	TaskTypePending TaskType = iota
	TaskTypeDownloading
	TaskTypeCompleted
	TaskTypeFailed
)

// UpdateDownloadSpeed 更新下载速度
func (m *Monitor) UpdateDownloadSpeed(projectID string, speed int64) error {
	ctx := context.Background()

	updates := map[string]interface{}{
		"download_speed": speed,
	}

	return m.redisClient.UpdateProjectStats(ctx, projectID, updates)
}

// RecordPackageUpload 记录包上传
func (m *Monitor) RecordPackageUpload(projectID string, size int64) error {
	ctx := context.Background()

	updates := map[string]interface{}{
		"total_size": size,
	}

	return m.redisClient.UpdateProjectStats(ctx, projectID, updates)
}
