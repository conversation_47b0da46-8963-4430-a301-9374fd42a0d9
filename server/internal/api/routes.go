package api

import (
	"context"
	"crypto/md5"
	"download-scheduler/server/internal/config"
	"download-scheduler/server/internal/mq"
	"download-scheduler/server/internal/storage"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/crypto/bcrypt"
)

var (
	mongoDB     *storage.MongoDB
	redisClient *storage.RedisClient
	upgrader    = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // 允许跨域
		},
	}
)

// API请求/响应结构体定义

type CreateProjectRequest struct {
	Name   string                `json:"name" binding:"required"`
	Config storage.ProjectConfig `json:"config" binding:"required"`
}

type CreateProjectResponse struct {
	ID    string `json:"id"`
	Token string `json:"token"`
}

type DownloadRequest struct {
	URL     string            `json:"url" binding:"required"`
	Method  string            `json:"method,omitempty"`
	Headers map[string]string `json:"headers,omitempty"`
}

type BatchTaskRequest struct {
	ProjectID string            `json:"projectId" binding:"required"`
	Requests  []DownloadRequest `json:"requests" binding:"required"`
	Priority  int               `json:"priority"`
}

type BatchTaskResponse struct {
	Message        string `json:"message"`
	TotalTasks     int    `json:"totalTasks"`
	PublishedTasks int    `json:"publishedTasks"`
	BatchID        string `json:"batchId"`
}

// Worker预注册请求
type WorkerPreRegisterRequest struct {
	Name         string                     `json:"name" binding:"required"`
	Description  string                     `json:"description"`
	Capabilities storage.WorkerCapabilities `json:"capabilities" binding:"required"`
}

// Worker激活请求
type WorkerActivateRequest struct {
	Host       string                   `json:"host" binding:"required"`
	Port       int                      `json:"port" binding:"required"`
	Version    string                   `json:"version" binding:"required"`
	SystemInfo storage.WorkerSystemInfo `json:"systemInfo" binding:"required"`
}

// Worker心跳请求
type WorkerHeartbeatRequest struct {
	Stats  storage.WorkerStats  `json:"stats" binding:"required"`
	Health storage.WorkerHealth `json:"health"`
}

type MonitoringData struct {
	Stats        *storage.ProjectStats        `json:"stats"`
	Workers      []*storage.Worker            `json:"workers"`
	QueueDepth   int64                        `json:"queueDepth"`
	SpeedHistory []map[string]interface{}     `json:"speedHistory"`
	WorkerStats  map[string]map[string]string `json:"workerStats"`
}

// WorkerWithStatus 包含实时状态的Worker信息
type WorkerWithStatus struct {
	*storage.Worker
	Status      string        `json:"status"`      // 实时计算的状态
	Stats       *WorkerStats  `json:"stats"`       // 实时统计信息
	Health      *WorkerHealth `json:"health"`      // 实时健康状态
	IsConnected bool          `json:"isConnected"` // 是否已连接
}

// 初始化数据库连接
func InitDatabase() error {
	cfg := config.GetConfig()

	// 初始化MongoDB
	var err error
	mongoDB, err = storage.NewMongoDB(
		cfg.Database.MongoDB.URI,
		cfg.Database.MongoDB.Database,
		cfg.Database.MongoDB.Timeout,
	)
	if err != nil {
		return err
	}

	// 初始化Redis
	redisClient, err = storage.NewRedisClient(
		cfg.Database.Redis.Addr,
		cfg.Database.Redis.Password,
		cfg.Database.Redis.DB,
	)
	if err != nil {
		return err
	}

	// 初始化Worker WebSocket连接管理器
	InitWorkerConnectionManager()

	return nil
}

// InitializeDefaultUsers 初始化默认用户
func InitializeDefaultUsers() error {
	ctx := context.Background()

	// 检查是否已经存在管理员用户
	users, err := mongoDB.ListUsers(ctx)
	if err != nil {
		return fmt.Errorf("failed to check existing users: %w", err)
	}

	// 如果已经有用户，则不需要初始化
	if len(users) > 0 {
		log.Printf("Users already exist, skipping default user creation")
		return nil
	}

	// 创建默认管理员用户
	adminUser := &storage.User{
		Username: "admin",
		Password: "admin123", // 这将在CreateUser中被加密
		Name:     "Administrator",
		Email:    "<EMAIL>",
		Role:     "admin",
		Status:   "active",
	}

	err = mongoDB.CreateUser(ctx, adminUser)
	if err != nil {
		return fmt.Errorf("failed to create default admin user: %w", err)
	}

	// 创建默认操作员用户
	operatorUser := &storage.User{
		Username: "operator",
		Password: "operator123", // 这将在CreateUser中被加密
		Name:     "Operator",
		Email:    "<EMAIL>",
		Role:     "operator",
		Status:   "active",
	}

	err = mongoDB.CreateUser(ctx, operatorUser)
	if err != nil {
		return fmt.Errorf("failed to create default operator user: %w", err)
	}

	log.Printf("Default users created successfully:")
	log.Printf("  Admin user: admin / admin123")
	log.Printf("  Operator user: operator / operator123")
	log.Printf("Please change these default passwords after first login!")

	return nil
}

// RegisterRoutes 注册所有API路由
func RegisterRoutes(r *gin.Engine) {
	// 中间件
	r.Use(CORSMiddleware())
	r.Use(LoggingMiddleware())

	api := r.Group("/api")

	// 用户认证路由 (无需认证)
	auth := api.Group("/auth")
	{
		auth.POST("/login", Login)
		auth.POST("/logout", UserAuthMiddleware(), Logout)
		auth.GET("/me", UserAuthMiddleware(), GetCurrentUser)
	}

	// 用户管理路由 (需要管理员权限)
	users := api.Group("/users")
	users.Use(UserAuthMiddleware())
	users.Use(AdminOnlyMiddleware())
	{
		users.POST("", CreateUser)
		users.GET("", GetUserList)
		users.GET("/:id", GetUser)
		users.PUT("/:id", UpdateUser)
		users.DELETE("/:id", DeleteUser)
		users.POST("/:id/change-password", ChangeUserPassword)
	}

	// 项目管理路由
	projects := api.Group("/projects")
	projects.Use(UserAuthMiddleware()) // 改为用户认证
	{
		projects.POST("", CreateProject)
		projects.GET("", GetProjectList)
		projects.GET("/:id", GetProject)
		projects.PUT("/:id", UpdateProject)
		projects.DELETE("/:id", DeleteProject)
		projects.POST("/:id/pause", PauseProject)
		projects.POST("/:id/resume", ResumeProject)
	}

	// 任务管理路由
	tasks := api.Group("/tasks")
	{
		tasks.POST("/batch", AuthMiddleware(), SubmitBatchTasks)           // 保持原有的项目token认证
		tasks.GET("/stats/:projectId", UserAuthMiddleware(), GetTaskStats) // 改为用户认证
	}

	// Worker管理路由
	workers := api.Group("/workers")
	{
		workers.POST("/register", UserAuthMiddleware(), PreRegisterWorker)      // 改为用户认证
		workers.GET("/connect", WorkerAuthMiddleware(), WorkerWebSocketHandler) // 新增WebSocket连接
		workers.POST("/activate", WorkerAuthMiddleware(), ActivateWorker)
		workers.POST("/heartbeat", WorkerAuthMiddleware(), WorkerHeartbeat) // 保留作为兼容性API
		workers.GET("/config", WorkerAuthMiddleware(), GetWorkerConfig)
		workers.GET("", UserAuthMiddleware(), GetWorkerList)                    // 改为用户认证
		workers.GET("/:id", UserAuthMiddleware(), GetWorker)                    // 改为用户认证
		workers.PUT("/:id", UserAuthMiddleware(), UpdateWorker)                 // 改为用户认证
		workers.POST("/:id/disable", UserAuthMiddleware(), DisableWorker)       // 改为用户认证
		workers.POST("/:id/enable", UserAuthMiddleware(), EnableWorker)         // 改为用户认证
		workers.POST("/:id/message", UserAuthMiddleware(), SendMessageToWorker) // 新增发送消息
		workers.DELETE("/:id", UserAuthMiddleware(), DeleteWorker)              // 改为用户认证
	}

	// 监控路由
	monitor := api.Group("/monitor")
	monitor.Use(UserAuthMiddleware()) // 改为用户认证
	{
		monitor.GET("/project/:id", GetProjectMonitoring)
		monitor.GET("/realtime/:projectId", RealtimeMonitoring)
	}
}

// 中间件

func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return ""
	})
}

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing authorization token"})
			c.Abort()
			return
		}

		log.Printf("token: %s", token)
		// 验证token
		project, err := mongoDB.GetProjectByToken(context.Background(), token)
		if err != nil {
			log.Printf("Invalid token: %s", err)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}
		log.Printf("project: %+v", project)

		c.Set("project", project)
		c.Next()
	}
}

func WorkerAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" || !strings.HasPrefix(token, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing or invalid authorization token"})
			c.Abort()
			return
		}

		token = strings.TrimPrefix(token, "Bearer ")
		worker, err := mongoDB.GetWorkerByToken(context.Background(), token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid worker token"})
			c.Abort()
			return
		}

		c.Set("worker", worker)
		c.Next()
	}
}

func UserAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing authorization token"})
			c.Abort()
			return
		}

		// 移除Bearer前缀（如果有）
		if strings.HasPrefix(token, "Bearer ") {
			token = strings.TrimPrefix(token, "Bearer ")
		}

		// 验证用户token
		user, err := mongoDB.GetUserByToken(context.Background(), token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// 检查用户状态
		if user.Status != "active" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User account is not active"})
			c.Abort()
			return
		}

		c.Set("user", user)
		c.Next()
	}
}

func AdminOnlyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found in context"})
			c.Abort()
			return
		}

		userObj, ok := user.(*storage.User)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user object"})
			c.Abort()
			return
		}

		if userObj.Role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin privileges required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// 项目管理接口

// CreateProject 创建项目
func CreateProject(c *gin.Context) {
	var req CreateProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	project := &storage.Project{
		Name:   req.Name,
		Config: req.Config,
		Status: "active",
	}

	err := mongoDB.CreateProject(context.Background(), project)
	if err != nil {
		log.Printf("Failed to create project: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project"})
		return
	}

	c.JSON(http.StatusOK, CreateProjectResponse{
		ID:    project.ID.Hex(),
		Token: project.Token,
	})
}

// GetProjectList 获取项目列表
func GetProjectList(c *gin.Context) {
	projects, err := mongoDB.ListProjects(context.Background())
	if err != nil {
		log.Printf("Failed to list projects: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list projects"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"projects": projects,
		"total":    len(projects),
	})
}

// GetProject 获取项目详情
func GetProject(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	project, err := mongoDB.GetProject(context.Background(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	c.JSON(http.StatusOK, project)
}

// UpdateProject 更新项目
func UpdateProject(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = mongoDB.UpdateProject(context.Background(), id, updates)
	if err != nil {
		log.Printf("Failed to update project: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project updated successfully"})
}

// DeleteProject 删除项目
func DeleteProject(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	err = mongoDB.DeleteProject(context.Background(), id)
	if err != nil {
		log.Printf("Failed to delete project: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete project"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project deleted successfully"})
}

// PauseProject 暂停项目
func PauseProject(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	// 更新项目状态
	err = mongoDB.UpdateProject(context.Background(), id, bson.M{"status": "paused"})
	if err != nil {
		log.Printf("Failed to pause project: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to pause project"})
		return
	}

	// 更新Redis控制状态
	err = redisClient.SetProjectControl(context.Background(), idStr, "paused")
	if err != nil {
		log.Printf("Failed to set project control: %s", err)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project paused successfully"})
}

// ResumeProject 恢复项目
func ResumeProject(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	// 更新项目状态
	err = mongoDB.UpdateProject(context.Background(), id, bson.M{"status": "active"})
	if err != nil {
		log.Printf("Failed to resume project: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to resume project"})
		return
	}

	// 更新Redis控制状态
	err = redisClient.SetProjectControl(context.Background(), idStr, "active")
	if err != nil {
		log.Printf("Failed to set project control: %s", err)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project resumed successfully"})
}

// 任务管理接口

// SubmitBatchTasks 批量提交任务
func SubmitBatchTasks(c *gin.Context) {
	var req BatchTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	project, exists := c.Get("project")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Project not found in context"})
		return
	}
	proj := project.(*storage.Project)

	// 检查项目状态
	status, _ := redisClient.GetProjectControl(context.Background(), proj.ID.Hex())
	if status == "paused" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project is paused"})
		return
	}

	// 创建批次记录
	batch := &storage.Batch{
		ProjectID:  proj.ID,
		BatchNo:    1, // 这里可以实现自动递增
		TotalCount: len(req.Requests),
		Status:     "processing",
		Stats: storage.BatchStats{
			Pending:     len(req.Requests),
			Downloading: 0,
			Completed:   0,
			Failed:      0,
		},
	}

	err := mongoDB.CreateBatch(context.Background(), batch)
	if err != nil {
		log.Printf("Failed to create batch: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create batch"})
		return
	}

	// 连接RabbitMQ
	cfg := config.GetConfig()
	publisher, err := mq.NewPublisher(cfg.MQ.RabbitMQ.URL)
	if err != nil {
		log.Printf("Failed to connect to RabbitMQ: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to connect to message queue"})
		return
	}
	defer publisher.Close()

	// 发布任务到队列
	queueName := "tasks." + req.ProjectID
	publishedCount := 0
	priority := req.Priority
	if priority == 0 {
		priority = 5 // 默认优先级
	}

	for _, request := range req.Requests {
		// 设置默认方法为GET
		method := request.Method
		if method == "" {
			method = "GET"
		}

		taskMsg := storage.TaskMessage{
			ID:         primitive.NewObjectID().Hex(),
			ProjectID:  req.ProjectID,
			BatchID:    batch.ID.Hex(),
			URL:        request.URL,
			Method:     method,
			Headers:    request.Headers,
			Priority:   priority,
			RetryCount: 0,
			OSSConfig:  proj.Config.OSSConfig,
		}

		body, err := json.Marshal(taskMsg)
		if err != nil {
			log.Printf("Failed to marshal task message for url %s: %s", request.URL, err)
			continue
		}

		err = publisher.Publish(context.Background(), queueName, body)
		if err != nil {
			log.Printf("Failed to publish message for url %s: %s", request.URL, err)
		} else {
			publishedCount++
		}
	}

	// 更新统计信息
	err = redisClient.IncrementProjectStats(context.Background(), req.ProjectID, "total_tasks", int64(publishedCount))
	if err != nil {
		log.Printf("Failed to update project stats: %s", err)
	}

	err = redisClient.IncrementProjectStats(context.Background(), req.ProjectID, "pending_tasks", int64(publishedCount))
	if err != nil {
		log.Printf("Failed to update pending tasks: %s", err)
	}

	c.JSON(http.StatusOK, BatchTaskResponse{
		Message:        "Tasks submitted successfully",
		TotalTasks:     len(req.Requests),
		PublishedTasks: publishedCount,
		BatchID:        batch.ID.Hex(),
	})
}

// GetTaskStats 获取任务统计
func GetTaskStats(c *gin.Context) {
	projectID := c.Param("projectId")

	stats, err := redisClient.GetProjectStats(context.Background(), projectID)
	if err != nil {
		log.Printf("Failed to get project stats: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get task stats"})
		return
	}

	// 计算成功率
	if stats.TotalTasks > 0 {
		stats.SuccessRate = float64(stats.CompletedTasks) / float64(stats.TotalTasks) * 100
	}

	c.JSON(http.StatusOK, stats)
}

// Worker管理接口

// PreRegisterWorker Worker预注册
func PreRegisterWorker(c *gin.Context) {
	var req WorkerPreRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 生成Worker Token
	token := generateWorkerToken()

	worker := &storage.Worker{
		Name:         req.Name,
		Description:  req.Description,
		Status:       "pending",
		Token:        token,
		Capabilities: req.Capabilities,
		Projects:     []string{},
		CreatedAt:    time.Now(),
	}

	err := mongoDB.CreateWorker(context.Background(), worker)
	if err != nil {
		log.Printf("Failed to create worker: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to register worker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":      worker.ID.Hex(),
		"token":   token,
		"message": "Worker预注册成功，请使用token启动Worker服务",
	})
}

// ActivateWorker Worker激活
func ActivateWorker(c *gin.Context) {
	var req WorkerActivateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	worker, exists := c.Get("worker")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Worker not found in context"})
		return
	}
	w := worker.(*storage.Worker)

	if w.Status != "pending" {
		c.JSON(http.StatusConflict, gin.H{"error": "Worker already activated"})
		return
	}

	// 更新激活信息
	now := time.Now()
	updates := bson.M{
		"status":        "active",
		"host":          req.Host,
		"port":          req.Port,
		"version":       req.Version,
		"systemInfo":    req.SystemInfo,
		"activatedAt":   &now,
		"lastHeartbeat": &now,
	}

	err := mongoDB.UpdateWorker(context.Background(), w.ID, updates)
	if err != nil {
		log.Printf("Failed to activate worker: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to activate worker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":          "Worker激活成功",
		"workerId":         w.ID.Hex(),
		"assignedProjects": []string{},
	})
}

// WorkerHeartbeat Worker心跳
func WorkerHeartbeat(c *gin.Context) {
	var req WorkerHeartbeatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	worker, exists := c.Get("worker")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Worker not found in context"})
		return
	}
	w := worker.(*storage.Worker)

	// 更新MongoDB中的心跳和统计信息
	err := mongoDB.UpdateWorkerHeartbeat(context.Background(), w.Token, &req.Stats, &req.Health)
	if err != nil {
		log.Printf("Failed to update worker heartbeat: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update heartbeat"})
		return
	}

	// 更新Redis中的实时统计
	stats := map[string]interface{}{
		"cpu_usage":     req.Stats.CPUUsage,
		"memory_usage":  req.Stats.MemoryUsage,
		"disk_usage":    req.Stats.DiskUsage,
		"active_tasks":  req.Stats.ActiveTasks,
		"network_speed": req.Stats.NetworkSpeed,
	}

	err = redisClient.SetWorkerStats(context.Background(), w.ID.Hex(), stats)
	if err != nil {
		log.Printf("Failed to set worker stats in Redis: %s", err)
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Heartbeat received",
		"assignments": gin.H{
			"newProjects":     []string{},
			"removedProjects": []string{},
		},
	})
}

// GetWorkerConfig 获取Worker配置
func GetWorkerConfig(c *gin.Context) {
	worker, exists := c.Get("worker")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Worker not found in context"})
		return
	}
	w := worker.(*storage.Worker)

	// 生成当前配置（这里可以从数据库或配置服务获取）
	config := getDefaultWorkerConfig(w.Name)

	c.JSON(http.StatusOK, config)
}

// GetWorkerList 获取Worker列表（基于WebSocket连接状态实时计算）
func GetWorkerList(c *gin.Context) {
	workers, err := mongoDB.ListWorkers(context.Background())
	if err != nil {
		log.Printf("Failed to list workers: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list workers"})
		return
	}

	// 获取WebSocket连接状态
	activeConnections := workerConnManager.GetActiveConnections()

	// 组合实时状态信息
	workersWithStatus := make([]*WorkerWithStatus, 0, len(workers))
	for _, worker := range workers {
		workerStatus := &WorkerWithStatus{
			Worker:      worker,
			IsConnected: false,
		}

		// 检查WebSocket连接状态
		if conn, exists := activeConnections[worker.ID]; exists {
			workerStatus.IsConnected = true
			workerStatus.Status = "active"
			stats, health := conn.GetStats()
			workerStatus.Stats = stats
			workerStatus.Health = health
		} else {
			// 没有WebSocket连接
			if worker.LastHeartbeat == nil {
				workerStatus.Status = "pending" // 从未激活
			} else {
				workerStatus.Status = "offline" // 已离线
			}
		}

		workersWithStatus = append(workersWithStatus, workerStatus)
	}

	c.JSON(http.StatusOK, gin.H{
		"workers": workersWithStatus,
		"total":   len(workersWithStatus),
	})
}

// GetWorker 获取Worker详情（包含实时状态）
func GetWorker(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid worker ID"})
		return
	}

	worker, err := mongoDB.GetWorker(context.Background(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Worker not found"})
		return
	}

	// 构建包含实时状态的Worker信息
	workerStatus := &WorkerWithStatus{
		Worker:      worker,
		IsConnected: false,
	}

	// 检查WebSocket连接状态
	if conn, exists := workerConnManager.GetConnectionByWorkerID(worker.ID); exists {
		workerStatus.IsConnected = true
		workerStatus.Status = "active"
		stats, health := conn.GetStats()
		workerStatus.Stats = stats
		workerStatus.Health = health
	} else {
		// 没有WebSocket连接
		if worker.LastHeartbeat == nil {
			workerStatus.Status = "pending" // 从未激活
		} else {
			workerStatus.Status = "offline" // 已离线
		}
	}

	c.JSON(http.StatusOK, workerStatus)
}

// UpdateWorker 更新Worker配置
func UpdateWorker(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid worker ID"})
		return
	}

	var req struct {
		Name         string                     `json:"name"`
		Description  string                     `json:"description"`
		Capabilities storage.WorkerCapabilities `json:"capabilities"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updates := bson.M{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Capabilities.MaxConcurrent > 0 || req.Capabilities.DiskSpace > 0 || req.Capabilities.Bandwidth > 0 {
		updates["capabilities"] = req.Capabilities
	}

	err = mongoDB.UpdateWorker(context.Background(), id, updates)
	if err != nil {
		log.Printf("Failed to update worker: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update worker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Worker配置更新成功"})
}

// DisableWorker 禁用Worker
func DisableWorker(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid worker ID"})
		return
	}

	updates := bson.M{"status": "disabled"}
	err = mongoDB.UpdateWorker(context.Background(), id, updates)
	if err != nil {
		log.Printf("Failed to disable worker: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to disable worker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Worker已禁用"})
}

// EnableWorker 启用Worker
func EnableWorker(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid worker ID"})
		return
	}

	updates := bson.M{"status": "active"}
	err = mongoDB.UpdateWorker(context.Background(), id, updates)
	if err != nil {
		log.Printf("Failed to enable worker: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to enable worker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Worker已启用"})
}

// DeleteWorker 删除Worker
func DeleteWorker(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid worker ID"})
		return
	}

	// 检查Worker状态，只允许删除离线的Worker
	worker, err := mongoDB.GetWorker(context.Background(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Worker not found"})
		return
	}

	// 检查WebSocket连接状态，不允许删除活跃的Worker
	if _, exists := workerConnManager.GetConnectionByWorkerID(worker.ID); exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete active worker, please stop the worker first"})
		return
	}

	err = mongoDB.DeleteWorker(context.Background(), id)
	if err != nil {
		log.Printf("Failed to delete worker: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete worker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Worker删除成功"})
}

// 监控接口

// GetProjectMonitoring 获取项目监控数据
func GetProjectMonitoring(c *gin.Context) {
	projectID := c.Param("id")

	// 获取项目统计
	stats, err := redisClient.GetProjectStats(context.Background(), projectID)
	if err != nil {
		log.Printf("Failed to get project stats: %s", err)
		stats = &storage.ProjectStats{}
	}

	// 获取Worker列表
	workers, err := mongoDB.ListWorkers(context.Background())
	if err != nil {
		log.Printf("Failed to get workers: %s", err)
		workers = []*storage.Worker{}
	}

	// 获取队列深度
	queueDepth, err := redisClient.GetQueueDepth(context.Background(), projectID)
	if err != nil {
		log.Printf("Failed to get queue depth: %s", err)
		queueDepth = 0
	}

	// 获取速度历史
	speedHistory, err := redisClient.GetSpeedHistory(context.Background(), projectID, 24)
	if err != nil {
		log.Printf("Failed to get speed history: %s", err)
		speedHistory = []map[string]interface{}{}
	}

	// 获取Worker实时统计
	workerStats, err := redisClient.GetAllWorkerStats(context.Background())
	if err != nil {
		log.Printf("Failed to get worker stats: %s", err)
		workerStats = make(map[string]map[string]string)
	}

	data := MonitoringData{
		Stats:        stats,
		Workers:      workers,
		QueueDepth:   queueDepth,
		SpeedHistory: speedHistory,
		WorkerStats:  workerStats,
	}

	c.JSON(http.StatusOK, data)
}

// RealtimeMonitoring WebSocket实时监控
func RealtimeMonitoring(c *gin.Context) {
	projectID := c.Param("projectId")

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection: %s", err)
		return
	}
	defer conn.Close()

	// 定时发送监控数据
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 获取最新监控数据
			stats, _ := redisClient.GetProjectStats(context.Background(), projectID)
			workers, _ := mongoDB.ListWorkers(context.Background())
			queueDepth, _ := redisClient.GetQueueDepth(context.Background(), projectID)
			workerStats, _ := redisClient.GetAllWorkerStats(context.Background())

			data := map[string]interface{}{
				"timestamp":   time.Now().Unix(),
				"stats":       stats,
				"workers":     workers,
				"queueDepth":  queueDepth,
				"workerStats": workerStats,
				"events":      []string{}, // 这里可以添加事件信息
			}

			err := conn.WriteJSON(data)
			if err != nil {
				log.Printf("Failed to write WebSocket message: %s", err)
				return
			}
		}
	}
}

// getDefaultWorkerConfig 生成默认的Worker配置
func getDefaultWorkerConfig(workerName string) map[string]interface{} {
	cfg := config.GetConfig()

	return map[string]interface{}{
		"workerName":        workerName,
		"heartbeatInterval": 5, // 5秒
		"rabbitmqUrl":       cfg.MQ.RabbitMQ.URL,
		"maxConcurrent":     5,
		"download": map[string]interface{}{
			"concurrent":  3,
			"timeout":     300, // 5分钟
			"retry":       3,
			"bufferSize":  1048576, // 1MB
			"userAgent":   "DownloadScheduler-Worker/1.0",
			"maxFileSize": 10737418240, // 10GB
		},
		"packager": map[string]interface{}{
			"tempDir":      "/data/temp",
			"compression":  "gzip",
			"maxSizeGb":    10,
			"checksumType": "md5",
		},
		"oss": map[string]interface{}{
			"uploadPartSize":   5242880, // 5MB
			"uploadConcurrent": 3,
			"timeout":          60, // 1分钟
		},
		"log": map[string]interface{}{
			"level": "info",
			"file":  "logs/worker.log",
		},
	}
}

// generateWorkerToken 生成Worker Token
func generateWorkerToken() string {
	hash := md5.Sum([]byte(fmt.Sprintf("worker_%d", time.Now().UnixNano())))
	return fmt.Sprintf("%x", hash)
}

// 用户认证相关接口

// Login 用户登录
func Login(c *gin.Context) {
	var req storage.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证用户凭据
	user, err := mongoDB.VerifyUserPassword(context.Background(), req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid username or password"})
		return
	}

	// 创建用户会话
	userAgent := c.GetHeader("User-Agent")
	ipAddress := c.ClientIP()
	session, err := mongoDB.CreateUserSession(context.Background(), user.ID, userAgent, ipAddress)
	if err != nil {
		log.Printf("Failed to create user session: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create session"})
		return
	}

	// 返回登录响应
	response := storage.LoginResponse{
		Token:     session.Token,
		User:      user,
		ExpiresAt: session.ExpiresAt.Unix(),
	}

	c.JSON(http.StatusOK, response)
}

// Logout 用户登出
func Logout(c *gin.Context) {
	token := c.GetHeader("Authorization")
	if strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}

	// 删除用户会话
	err := mongoDB.DeleteUserSession(context.Background(), token)
	if err != nil {
		log.Printf("Failed to delete user session: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to logout"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Logged out successfully"})
}

// GetCurrentUser 获取当前用户信息
func GetCurrentUser(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	userObj, ok := user.(*storage.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user object"})
		return
	}

	c.JSON(http.StatusOK, userObj)
}

// 用户管理接口

// CreateUser 创建用户
func CreateUser(c *gin.Context) {
	var req storage.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证角色
	if req.Role != "admin" && req.Role != "operator" && req.Role != "viewer" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role. Must be admin, operator, or viewer"})
		return
	}

	user := &storage.User{
		Username: req.Username,
		Password: req.Password,
		Name:     req.Name,
		Email:    req.Email,
		Role:     req.Role,
		Status:   "active",
	}

	err := mongoDB.CreateUser(context.Background(), user)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			c.JSON(http.StatusConflict, gin.H{"error": "Username already exists"})
			return
		}
		log.Printf("Failed to create user: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User created successfully",
		"id":      user.ID.Hex(),
	})
}

// GetUserList 获取用户列表
func GetUserList(c *gin.Context) {
	users, err := mongoDB.ListUsers(context.Background())
	if err != nil {
		log.Printf("Failed to list users: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list users"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"users": users,
		"total": len(users),
	})
}

// GetUser 获取用户详情
func GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	user, err := mongoDB.GetUserByID(context.Background(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUser 更新用户
func UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 不允许通过此接口更新密码
	delete(updates, "password")

	// 验证角色
	if role, exists := updates["role"]; exists {
		roleStr, ok := role.(string)
		if !ok || (roleStr != "admin" && roleStr != "operator" && roleStr != "viewer") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role. Must be admin, operator, or viewer"})
			return
		}
	}

	err = mongoDB.UpdateUser(context.Background(), id, updates)
	if err != nil {
		log.Printf("Failed to update user: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
}

// DeleteUser 删除用户
func DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// 获取当前用户，确保不能删除自己
	currentUser, exists := c.Get("user")
	if exists {
		if userObj, ok := currentUser.(*storage.User); ok && userObj.ID == id {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete yourself"})
			return
		}
	}

	err = mongoDB.DeleteUser(context.Background(), id)
	if err != nil {
		log.Printf("Failed to delete user: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// ChangeUserPassword 修改用户密码
func ChangeUserPassword(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req storage.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取用户
	user, err := mongoDB.GetUserByID(context.Background(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 验证旧密码
	_, err = mongoDB.VerifyUserPassword(context.Background(), user.Username, req.OldPassword)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid old password"})
		return
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Failed to hash password: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}

	// 更新密码
	err = mongoDB.UpdateUser(context.Background(), id, bson.M{"password": string(hashedPassword)})
	if err != nil {
		log.Printf("Failed to update password: %s", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}

	// 删除用户的所有会话，强制重新登录
	err = mongoDB.DeleteUserSessionsByUserID(context.Background(), id)
	if err != nil {
		log.Printf("Failed to delete user sessions: %s", err)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Password changed successfully"})
}

// SendMessageToWorker 向指定Worker发送消息
func SendMessageToWorker(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid worker ID"})
		return
	}

	var req struct {
		Type string      `json:"type" binding:"required"`
		Data interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查Worker是否存在
	worker, err := mongoDB.GetWorker(context.Background(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Worker not found"})
		return
	}

	// 构建消息
	message := map[string]interface{}{
		"type": req.Type,
		"data": req.Data,
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to marshal message"})
		return
	}

	// 通过WebSocket连接发送消息
	err = workerConnManager.SendToWorker(worker.ID, messageBytes)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "Worker is not connected or failed to send message",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Message sent successfully",
		"worker":  worker.Name,
		"type":    req.Type,
	})
}

// BroadcastMessageToWorkers 向所有在线Worker广播消息
func BroadcastMessageToWorkers(c *gin.Context) {
	var req struct {
		Type string      `json:"type" binding:"required"`
		Data interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 构建消息
	message := map[string]interface{}{
		"type": req.Type,
		"data": req.Data,
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to marshal message"})
		return
	}

	// 广播消息
	workerConnManager.BroadcastToWorkers(messageBytes)

	c.JSON(http.StatusOK, gin.H{
		"message": "Message broadcasted successfully",
		"type":    req.Type,
		"workers": workerConnManager.GetWorkerCount(),
	})
}
