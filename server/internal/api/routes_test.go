package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestCORSMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(CORSMiddleware())

	router.GET("/test", func(c *gin.Context) {
		c.J<PERSON>(200, gin.H{"message": "test"})
	})

	// 测试CORS头
	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.<PERSON><PERSON>().Get("Access-Control-Allow-Origin") != "*" {
		t.Error("CORS Allow-Origin header not set correctly")
	}

	if w.<PERSON><PERSON>().Get("Access-Control-Allow-Methods") != "POST, OPTIONS, GET, PUT, DELETE" {
		t.Error("CORS Allow-Methods header not set correctly")
	}
}

func TestOPTIONSRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(CORSMiddleware())

	router.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "test"})
	})

	// 测试OPTIONS请求
	req, _ := http.NewRequest("OPTIONS", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != 204 {
		t.Errorf("Expected status 204 for OPTIONS request, got %d", w.Code)
	}
}

func TestCreateProjectRequest(t *testing.T) {
	// 测试CreateProjectRequest结构体的JSON绑定
	jsonData := `{
		"name": "Test Project", 
		"config": {
			"packSizeGB": 10,
			"concurrent": 5,
			"retryTimes": 3,
			"downloadTimeout": 300,
			"ossConfig": {
				"provider": "aliyun",
				"endpoint": "oss-cn-beijing.aliyuncs.com",
				"bucket": "test-bucket",
				"accessKey": "test-key",
				"secretKey": "test-secret",
				"prefix": "test/",
				"region": "cn-beijing",
				"forcePathStyle": false
			}
		}
	}`

	var req CreateProjectRequest
	err := json.Unmarshal([]byte(jsonData), &req)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	if req.Name != "Test Project" {
		t.Errorf("Expected name 'Test Project', got %s", req.Name)
	}

	if req.Config.PackSizeGB != 10 {
		t.Errorf("Expected packSizeGB 10, got %d", req.Config.PackSizeGB)
	}

	if req.Config.OSSConfig.Provider != "aliyun" {
		t.Errorf("Expected provider 'aliyun', got %s", req.Config.OSSConfig.Provider)
	}
}

func TestBatchTaskRequest(t *testing.T) {
	// 测试BatchTaskRequest结构体
	jsonData := `{
		"projectId": "507f1f77bcf86cd799439011",
		"requests": [
			{
				"url": "https://example.com/file1.zip",
				"method": "GET",
				"headers": {
					"User-Agent": "TestAgent/1.0"
				}
			},
			{
				"url": "https://example.com/file2.zip",
				"method": "POST",
				"headers": {
					"Content-Type": "application/json"
				}
			}
		],
		"priority": 5
	}`

	var req BatchTaskRequest
	err := json.Unmarshal([]byte(jsonData), &req)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	if req.ProjectID != "507f1f77bcf86cd799439011" {
		t.Errorf("Expected projectId '507f1f77bcf86cd799439011', got %s", req.ProjectID)
	}

	if len(req.Requests) != 2 {
		t.Errorf("Expected 2 requests, got %d", len(req.Requests))
	}

	if req.Requests[0].URL != "https://example.com/file1.zip" {
		t.Errorf("Expected first URL 'https://example.com/file1.zip', got %s", req.Requests[0].URL)
	}

	if req.Requests[0].Method != "GET" {
		t.Errorf("Expected first method 'GET', got %s", req.Requests[0].Method)
	}

	if req.Requests[0].Headers["User-Agent"] != "TestAgent/1.0" {
		t.Errorf("Expected User-Agent header 'TestAgent/1.0', got %s", req.Requests[0].Headers["User-Agent"])
	}

	if req.Priority != 5 {
		t.Errorf("Expected priority 5, got %d", req.Priority)
	}
}

func TestWorkerRegisterRequest(t *testing.T) {
	// 测试WorkerRegisterRequest结构体
	jsonData := `{
		"token": "test-worker-token-123456",
		"host": "*************",
		"port": 8081,
		"version": "1.0.0",
		"capabilities": {
			"maxConcurrent": 10,
			"diskSpace": 500,
			"bandwidth": 1000
		}
	}`

	var req WorkerRegisterRequest
	err := json.Unmarshal([]byte(jsonData), &req)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	if req.Token != "test-worker-token-123456" {
		t.Errorf("Expected token 'test-worker-token-123456', got %s", req.Token)
	}

	if req.Host != "*************" {
		t.Errorf("Expected host '*************', got %s", req.Host)
	}

	if req.Port != 8081 {
		t.Errorf("Expected port 8081, got %d", req.Port)
	}

	if req.Capabilities.MaxConcurrent != 10 {
		t.Errorf("Expected maxConcurrent 10, got %d", req.Capabilities.MaxConcurrent)
	}
}

// 测试健康检查接口（这是一个简单的测试示例）
func TestHealthCheck(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":    "ok",
			"timestamp": "2024-01-15T10:00:00Z",
		})
	})

	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != 200 {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to parse response: %v", err)
	}

	if response["status"] != "ok" {
		t.Errorf("Expected status 'ok', got %v", response["status"])
	}
}

func TestJSONBinding(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.POST("/test", func(c *gin.Context) {
		var req CreateProjectRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		c.JSON(200, gin.H{"received": req.Name})
	})

	// 测试有效JSON
	validJSON := `{"name": "Test Project", "config": {"packSizeGB": 10, "ossConfig": {}}}`
	req, _ := http.NewRequest("POST", "/test", bytes.NewBufferString(validJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != 200 {
		t.Errorf("Expected status 200 for valid JSON, got %d", w.Code)
	}

	// 测试无效JSON
	invalidJSON := `{"name": ""}`
	req, _ = http.NewRequest("POST", "/test", bytes.NewBufferString(invalidJSON))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != 400 {
		t.Errorf("Expected status 400 for invalid JSON, got %d", w.Code)
	}
}
