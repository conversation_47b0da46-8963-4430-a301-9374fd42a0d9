package api

import (
	"context"
	"encoding/json"
	"errors"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"download-scheduler/server/internal/storage"
)

// WebSocket 升级器配置
var wsUpgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 在生产环境中应该检查 Origin
		return true
	},
}

// WorkerConnection Worker WebSocket 连接
type WorkerConnection struct {
	ID       primitive.ObjectID `json:"id"`
	Worker   *storage.Worker    `json:"worker"`
	Conn     *websocket.Conn    `json:"-"`
	Send     chan []byte        `json:"-"`
	LastSeen time.Time          `json:"lastSeen"`
	Stats    *WorkerStats       `json:"stats,omitempty"`
	Health   *WorkerHealth      `json:"health,omitempty"`
	mutex    sync.RWMutex       `json:"-"`
}

// WorkerStats Worker 统计信息
type WorkerStats struct {
	CPUUsage       float64 `json:"cpuUsage"`
	MemoryUsage    float64 `json:"memoryUsage"`
	DiskUsage      float64 `json:"diskUsage"`
	ActiveTasks    int     `json:"activeTasks"`
	TotalProcessed int64   `json:"totalProcessed"`
	TotalFailed    int64   `json:"totalFailed"`
	NetworkSpeed   int64   `json:"networkSpeed"`
}

// WorkerHealth Worker 健康状态
type WorkerHealth struct {
	Status    string `json:"status"` // healthy|warning|error
	Uptime    int64  `json:"uptime"` // 运行时间(秒)
	LastError string `json:"lastError,omitempty"`
}

// WorkerMessage Worker 消息格式
type WorkerMessage struct {
	Type string      `json:"type"` // activate|stats|ping|pong
	Data interface{} `json:"data"`
}

// ActivateData Worker 激活数据
type ActivateData struct {
	Host       string                   `json:"host"`
	Port       int                      `json:"port"`
	Version    string                   `json:"version"`
	SystemInfo storage.WorkerSystemInfo `json:"systemInfo"`
}

// StatsData Worker 统计数据
type StatsData struct {
	Stats  WorkerStats  `json:"stats"`
	Health WorkerHealth `json:"health"`
}

// WorkerConnectionManager Worker 连接管理器
type WorkerConnectionManager struct {
	connections map[primitive.ObjectID]*WorkerConnection
	register    chan *WorkerConnection
	unregister  chan *WorkerConnection
	broadcast   chan []byte
	mutex       sync.RWMutex
}

// NewWorkerConnectionManager 创建连接管理器
func NewWorkerConnectionManager() *WorkerConnectionManager {
	return &WorkerConnectionManager{
		connections: make(map[primitive.ObjectID]*WorkerConnection),
		register:    make(chan *WorkerConnection),
		unregister:  make(chan *WorkerConnection),
		broadcast:   make(chan []byte),
	}
}

// Run 运行连接管理器
func (wcm *WorkerConnectionManager) Run() {
	for {
		select {
		case conn := <-wcm.register:
			wcm.mutex.Lock()
			wcm.connections[conn.ID] = conn
			wcm.mutex.Unlock()
			log.Printf("Worker %s connected, total connections: %d",
				conn.Worker.Name, len(wcm.connections))

		case conn := <-wcm.unregister:
			wcm.mutex.Lock()
			if _, ok := wcm.connections[conn.ID]; ok {
				delete(wcm.connections, conn.ID)
				close(conn.Send)
				wcm.mutex.Unlock()
				log.Printf("Worker %s disconnected, total connections: %d",
					conn.Worker.Name, len(wcm.connections))
			} else {
				wcm.mutex.Unlock()
			}

		case message := <-wcm.broadcast:
			wcm.mutex.RLock()
			for _, conn := range wcm.connections {
				select {
				case conn.Send <- message:
				default:
					delete(wcm.connections, conn.ID)
					close(conn.Send)
				}
			}
			wcm.mutex.RUnlock()
		}
	}
}

// GetActiveConnections 获取活跃连接
func (wcm *WorkerConnectionManager) GetActiveConnections() map[primitive.ObjectID]*WorkerConnection {
	wcm.mutex.RLock()
	defer wcm.mutex.RUnlock()

	result := make(map[primitive.ObjectID]*WorkerConnection)
	for id, conn := range wcm.connections {
		result[id] = conn
	}
	return result
}

// GetConnectionByWorkerID 根据 Worker ID 获取连接
func (wcm *WorkerConnectionManager) GetConnectionByWorkerID(workerID primitive.ObjectID) (*WorkerConnection, bool) {
	wcm.mutex.RLock()
	defer wcm.mutex.RUnlock()

	conn, exists := wcm.connections[workerID]
	return conn, exists
}

// SendToWorker 向指定 Worker 发送消息
func (wcm *WorkerConnectionManager) SendToWorker(workerID primitive.ObjectID, message []byte) error {
	wcm.mutex.RLock()
	conn, exists := wcm.connections[workerID]
	wcm.mutex.RUnlock()

	if !exists {
		return ErrWorkerNotConnected
	}

	select {
	case conn.Send <- message:
		return nil
	default:
		return ErrWorkerSendTimeout
	}
}

// BroadcastToWorkers 向所有 Worker 广播消息
func (wcm *WorkerConnectionManager) BroadcastToWorkers(message []byte) {
	wcm.broadcast <- message
}

// GetWorkerCount 获取连接的 Worker 数量
func (wcm *WorkerConnectionManager) GetWorkerCount() int {
	wcm.mutex.RLock()
	defer wcm.mutex.RUnlock()
	return len(wcm.connections)
}

// 全局连接管理器实例
var workerConnManager *WorkerConnectionManager

// InitWorkerConnectionManager 初始化连接管理器
func InitWorkerConnectionManager() {
	workerConnManager = NewWorkerConnectionManager()
	go workerConnManager.Run()
	log.Println("Worker WebSocket connection manager started")
}

// WorkerWebSocketHandler WebSocket 连接处理器
func WorkerWebSocketHandler(c *gin.Context) {
	// 获取 Worker 认证信息
	worker, exists := c.Get("worker")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Worker authentication required"})
		return
	}
	w := worker.(*storage.Worker)

	// 升级到 WebSocket
	conn, err := wsUpgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade WebSocket for worker %s: %v", w.Name, err)
		return
	}

	// 创建 Worker 连接
	workerConn := &WorkerConnection{
		ID:       w.ID,
		Worker:   w,
		Conn:     conn,
		Send:     make(chan []byte, 256),
		LastSeen: time.Now(),
	}

	// 注册连接
	workerConnManager.register <- workerConn

	// 启动连接处理协程
	go workerConn.writePump()
	go workerConn.readPump()
}

// readPump 读取消息泵
func (wc *WorkerConnection) readPump() {
	defer func() {
		workerConnManager.unregister <- wc
		wc.Conn.Close()
	}()

	wc.Conn.SetReadLimit(512)
	wc.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	wc.Conn.SetPongHandler(func(string) error {
		wc.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		wc.updateLastSeen()
		return nil
	})

	for {
		_, messageBytes, err := wc.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error for worker %s: %v", wc.Worker.Name, err)
			}
			break
		}

		wc.updateLastSeen()

		// 处理消息
		if err := wc.handleMessage(messageBytes); err != nil {
			log.Printf("Failed to handle message from worker %s: %v", wc.Worker.Name, err)
		}
	}
}

// writePump 写入消息泵
func (wc *WorkerConnection) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		wc.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-wc.Send:
			wc.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				wc.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := wc.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				return
			}

		case <-ticker.C:
			wc.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := wc.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// updateLastSeen 更新最后见到时间
func (wc *WorkerConnection) updateLastSeen() {
	wc.mutex.Lock()
	wc.LastSeen = time.Now()
	wc.mutex.Unlock()
}

// handleMessage 处理接收到的消息
func (wc *WorkerConnection) handleMessage(messageBytes []byte) error {
	var msg WorkerMessage
	if err := json.Unmarshal(messageBytes, &msg); err != nil {
		return err
	}

	switch msg.Type {
	case "activate":
		return wc.handleActivate(msg.Data)
	case "stats":
		return wc.handleStats(msg.Data)
	case "ping":
		return wc.handlePing()
	default:
		log.Printf("Unknown message type from worker %s: %s", wc.Worker.Name, msg.Type)
	}

	return nil
}

// handleActivate 处理激活消息
func (wc *WorkerConnection) handleActivate(data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var activateData ActivateData
	if err := json.Unmarshal(dataBytes, &activateData); err != nil {
		return err
	}

	// 更新 Worker 信息到数据库
	ctx := context.Background()
	now := time.Now()
	updates := map[string]interface{}{
		"host":          activateData.Host,
		"port":          activateData.Port,
		"version":       activateData.Version,
		"systemInfo":    activateData.SystemInfo,
		"activatedAt":   &now,
		"lastHeartbeat": &now,
	}

	err = mongoDB.UpdateWorker(ctx, wc.Worker.ID, updates)
	if err != nil {
		return err
	}

	log.Printf("Worker %s activated via WebSocket", wc.Worker.Name)

	// 发送激活确认
	response := WorkerMessage{
		Type: "activate_ack",
		Data: map[string]interface{}{
			"message":          "Worker activated successfully",
			"workerId":         wc.Worker.ID.Hex(),
			"assignedProjects": []string{},
		},
	}

	responseBytes, _ := json.Marshal(response)
	select {
	case wc.Send <- responseBytes:
	default:
	}

	return nil
}

// handleStats 处理统计消息
func (wc *WorkerConnection) handleStats(data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var statsData StatsData
	if err := json.Unmarshal(dataBytes, &statsData); err != nil {
		return err
	}

	// 更新连接中的统计信息
	wc.mutex.Lock()
	wc.Stats = &statsData.Stats
	wc.Health = &statsData.Health
	wc.mutex.Unlock()

	// 更新 Redis 中的实时统计
	stats := map[string]interface{}{
		"cpu_usage":     statsData.Stats.CPUUsage,
		"memory_usage":  statsData.Stats.MemoryUsage,
		"disk_usage":    statsData.Stats.DiskUsage,
		"active_tasks":  statsData.Stats.ActiveTasks,
		"network_speed": statsData.Stats.NetworkSpeed,
	}

	ctx := context.Background()
	err = redisClient.SetWorkerStats(ctx, wc.Worker.ID.Hex(), stats)
	if err != nil {
		log.Printf("Failed to set worker stats in Redis: %s", err)
	}

	return nil
}

// handlePing 处理 ping 消息
func (wc *WorkerConnection) handlePing() error {
	response := WorkerMessage{
		Type: "pong",
		Data: map[string]interface{}{
			"timestamp": time.Now().Unix(),
		},
	}

	responseBytes, _ := json.Marshal(response)
	select {
	case wc.Send <- responseBytes:
	default:
	}

	return nil
}

// GetStats 获取统计信息（线程安全）
func (wc *WorkerConnection) GetStats() (*WorkerStats, *WorkerHealth) {
	wc.mutex.RLock()
	defer wc.mutex.RUnlock()
	return wc.Stats, wc.Health
}

// 错误定义
var (
	ErrWorkerNotConnected = errors.New("worker not connected")
	ErrWorkerSendTimeout  = errors.New("worker send timeout")
)
