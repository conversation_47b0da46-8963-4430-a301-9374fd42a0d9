package config

import (
	"os"
	"testing"
	"time"
)

func TestLoadConfig(t *testing.T) {
	// 创建测试配置文件
	testConfig := `
server:
  port: 8080
  mode: debug

database:
  mongodb:
    uri: mongodb://localhost:27017
    database: test_db
    timeout: 10s
  redis:
    addr: localhost:6379
    password: ""
    db: 0

mq:
  rabbitmq:
    url: amqp://guest:guest@localhost:5672/
    prefetch: 10

monitoring:
  enable: true
  stats_interval: 5s

log:
  level: info
  file: logs/test.log
`

	// 写入临时文件
	tmpFile, err := os.CreateTemp("", "config_test_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(testConfig); err != nil {
		t.Fatalf("Failed to write config: %v", err)
	}
	tmpFile.Close()

	// 测试加载配置
	config, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// 验证配置值
	if config.Server.Port != 8080 {
		t.Errorf("Expected port 8080, got %d", config.Server.Port)
	}

	if config.Server.Mode != "debug" {
		t.Errorf("Expected mode debug, got %s", config.Server.Mode)
	}

	if config.Database.MongoDB.URI != "mongodb://localhost:27017" {
		t.Errorf("Expected MongoDB URI, got %s", config.Database.MongoDB.URI)
	}

	if config.Monitoring.StatsInterval != 5*time.Second {
		t.Errorf("Expected 5s interval, got %v", config.Monitoring.StatsInterval)
	}
}

func TestGlobalConfig(t *testing.T) {
	cfg := &Config{
		Server: ServerConfig{
			Port: 9090,
			Mode: "test",
		},
	}

	// 直接设置全局配置
	GlobalConfig = cfg
	retrieved := GetConfig()

	if retrieved.Server.Port != 9090 {
		t.Errorf("Expected port 9090, got %d", retrieved.Server.Port)
	}

	if retrieved.Server.Mode != "test" {
		t.Errorf("Expected mode test, got %s", retrieved.Server.Mode)
	}
}

func TestDefaultConfig(t *testing.T) {
	config := &Config{}

	// 验证默认值设置
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}

	if config.Server.Mode == "" {
		config.Server.Mode = "release"
	}

	if config.Server.Port != 8080 {
		t.Errorf("Expected default port 8080, got %d", config.Server.Port)
	}

	if config.Server.Mode != "release" {
		t.Errorf("Expected default mode release, got %s", config.Server.Mode)
	}
}
