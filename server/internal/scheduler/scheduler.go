package scheduler

import (
	"context"
	"download-scheduler/server/internal/config"
	"download-scheduler/server/internal/mq"
	"download-scheduler/server/internal/storage"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"
)

// Scheduler 任务调度器
type Scheduler struct {
	mongoDB     *storage.MongoDB
	redisClient *storage.RedisClient
	publisher   *mq.Publisher
	config      *config.Config
	stop        chan struct{}
	wg          sync.WaitGroup
}

// NewScheduler 创建新的调度器
func NewScheduler(mongoDB *storage.MongoDB, redisClient *storage.RedisClient, cfg *config.Config) (*Scheduler, error) {
	publisher, err := mq.NewPublisher(cfg.MQ.RabbitMQ.URL)
	if err != nil {
		return nil, err
	}

	return &Scheduler{
		mongoDB:     mongoDB,
		redisClient: redisClient,
		publisher:   publisher,
		config:      cfg,
		stop:        make(chan struct{}),
	}, nil
}

// Start 启动调度器
func (s *Scheduler) Start() {
	log.Println("Starting task scheduler...")

	// 启动队列监控器
	s.wg.Add(1)
	go s.runQueueMonitor()

	// 启动负载均衡器
	s.wg.Add(1)
	go s.runLoadBalancer()

	// 启动任务重试器
	s.wg.Add(1)
	go s.runTaskRetrier()

	log.Println("Task scheduler started")
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	log.Println("Stopping task scheduler...")
	close(s.stop)
	s.wg.Wait()
	if s.publisher != nil {
		s.publisher.Close()
	}
	log.Println("Task scheduler stopped")
}

// runQueueMonitor 运行队列监控器
func (s *Scheduler) runQueueMonitor() {
	defer s.wg.Done()

	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.monitorQueues()
		case <-s.stop:
			return
		}
	}
}

// monitorQueues 监控所有项目的队列状态
func (s *Scheduler) monitorQueues() {
	ctx := context.Background()

	// 获取所有活跃项目
	projects, err := s.mongoDB.ListProjects(ctx)
	if err != nil {
		log.Printf("Failed to list projects for queue monitoring: %s", err)
		return
	}

	for _, project := range projects {
		if project.Status != "active" {
			continue
		}

		projectID := project.ID.Hex()

		// 这里需要实际的队列深度查询逻辑
		// 由于我们使用RabbitMQ，需要通过RabbitMQ管理API获取队列深度
		queueDepth := s.getQueueDepth(projectID)

		// 更新Redis中的队列深度
		err = s.redisClient.SetQueueDepth(ctx, projectID, queueDepth)
		if err != nil {
			log.Printf("Failed to set queue depth for project %s: %s", projectID, err)
		}

		// 检查队列是否积压
		if queueDepth > 100000 { // 如果队列深度超过10万
			log.Printf("Queue backlog detected for project %s: %d tasks", projectID, queueDepth)
			s.handleQueueBacklog(projectID, queueDepth)
		}
	}
}

// getQueueDepth 获取队列深度（需要实现RabbitMQ管理API调用）
func (s *Scheduler) getQueueDepth(projectID string) int64 {
	// 这里应该调用RabbitMQ管理API获取队列深度
	// 现在返回模拟值
	return 0
}

// handleQueueBacklog 处理队列积压
func (s *Scheduler) handleQueueBacklog(projectID string, queueDepth int64) {
	log.Printf("Handling queue backlog for project %s with %d tasks", projectID, queueDepth)

	// 可以实现以下策略：
	// 1. 通知管理员
	// 2. 自动扩容Worker
	// 3. 调整任务优先级
	// 4. 限制新任务提交
}

// runLoadBalancer 运行负载均衡器
func (s *Scheduler) runLoadBalancer() {
	defer s.wg.Done()

	ticker := time.NewTicker(1 * time.Minute) // 每分钟运行一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.balanceLoad()
		case <-s.stop:
			return
		}
	}
}

// balanceLoad 负载均衡
func (s *Scheduler) balanceLoad() {
	ctx := context.Background()

	// 获取所有活跃的Worker
	workers, err := s.mongoDB.ListWorkers(ctx)
	if err != nil {
		log.Printf("Failed to list workers for load balancing: %s", err)
		return
	}

	activeWorkers := make([]*storage.Worker, 0)
	for _, worker := range workers {
		if worker.Status == "active" && time.Since(worker.LastHeartbeat) < 2*time.Minute {
			activeWorkers = append(activeWorkers, worker)
		}
	}

	if len(activeWorkers) == 0 {
		log.Println("No active workers available for load balancing")
		return
	}

	// 获取Worker实时统计
	workerStats, err := s.redisClient.GetAllWorkerStats(ctx)
	if err != nil {
		log.Printf("Failed to get worker stats for load balancing: %s", err)
		return
	}

	// 分析负载并做出调整
	s.analyzeAndAdjustLoad(activeWorkers, workerStats)
}

// analyzeAndAdjustLoad 分析负载并调整
func (s *Scheduler) analyzeAndAdjustLoad(workers []*storage.Worker, stats map[string]map[string]string) {
	// 计算平均负载
	totalCPU := 0.0
	totalMemory := 0.0
	totalTasks := 0
	validWorkers := 0

	for _, worker := range workers {
		workerID := worker.ID.Hex()
		if stat, ok := stats[workerID]; ok {
			if cpuUsage, exists := stat["cpu_usage"]; exists {
				// 解析CPU使用率
				// totalCPU += parseFloat(cpuUsage)
				_ = cpuUsage
			}
			if memoryUsage, exists := stat["memory_usage"]; exists {
				// 解析内存使用率
				// totalMemory += parseFloat(memoryUsage)
				_ = memoryUsage
			}
			if activeTasks, exists := stat["active_tasks"]; exists {
				// 解析活跃任务数
				// totalTasks += parseInt(activeTasks)
				_ = activeTasks
			}
			validWorkers++
		}
	}

	if validWorkers == 0 {
		return
	}

	avgCPU := totalCPU / float64(validWorkers)
	avgMemory := totalMemory / float64(validWorkers)
	avgTasks := float64(totalTasks) / float64(validWorkers)

	log.Printf("Load balancing stats - Avg CPU: %.2f%%, Avg Memory: %.2f%%, Avg Tasks: %.2f",
		avgCPU, avgMemory, avgTasks)

	// 这里可以实现具体的负载均衡策略
	// 例如：重新分配任务、调整Worker优先级等
}

// runTaskRetrier 运行任务重试器
func (s *Scheduler) runTaskRetrier() {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Minute) // 每5分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.retryFailedTasks()
		case <-s.stop:
			return
		}
	}
}

// retryFailedTasks 重试失败的任务
func (s *Scheduler) retryFailedTasks() {
	ctx := context.Background()

	// 获取所有活跃项目
	projects, err := s.mongoDB.ListProjects(ctx)
	if err != nil {
		log.Printf("Failed to list projects for task retry: %s", err)
		return
	}

	for _, project := range projects {
		if project.Status != "active" {
			continue
		}

		projectID := project.ID.Hex()

		// 这里需要实现从死信队列或失败任务表中获取失败任务的逻辑
		// 然后根据重试次数和重试策略决定是否重试
		s.processFailedTasksForProject(projectID, project.Config.RetryTimes)
	}
}

// processFailedTasksForProject 处理特定项目的失败任务
func (s *Scheduler) processFailedTasksForProject(projectID string, maxRetries int) {
	// 这里应该实现：
	// 1. 查询失败的任务
	// 2. 检查重试次数
	// 3. 重新提交符合条件的任务
	// 4. 更新任务状态

	log.Printf("Processing failed tasks for project %s (max retries: %d)", projectID, maxRetries)
}

// DistributeTasks 分发任务到Worker
func (s *Scheduler) DistributeTasks(projectID string, tasks []storage.TaskMessage) error {
	ctx := context.Background()

	// 获取项目配置
	project, err := s.mongoDB.GetProjectByToken(ctx, "Bearer_"+projectID) // 这里需要正确的token获取方式
	if err != nil {
		return err
	}

	// 检查项目状态
	status, _ := s.redisClient.GetProjectControl(ctx, projectID)
	if status == "paused" {
		log.Printf("Project %s is paused, skipping task distribution", projectID)
		return nil
	}

	queueName := "tasks." + projectID
	distributedCount := 0

	for _, task := range tasks {
		// 设置任务的OSS配置
		task.OSSConfig = project.Config.OSSConfig

		// 序列化任务
		body, err := s.marshalTask(task)
		if err != nil {
			log.Printf("Failed to marshal task %s: %s", task.ID, err)
			continue
		}

		// 发布到队列
		err = s.publisher.Publish(ctx, queueName, body)
		if err != nil {
			log.Printf("Failed to publish task %s: %s", task.ID, err)
		} else {
			distributedCount++
		}
	}

	log.Printf("Distributed %d/%d tasks for project %s", distributedCount, len(tasks), projectID)
	return nil
}

// marshalTask 序列化任务
func (s *Scheduler) marshalTask(task storage.TaskMessage) ([]byte, error) {
	// 使用json.Marshal序列化任务
	body, err := json.Marshal(task)
	if err != nil {
		return nil, fmt.Errorf("marshal task: %w", err)
	}
	return body, nil
}

// GetSchedulerStats 获取调度器统计信息
func (s *Scheduler) GetSchedulerStats() *SchedulerStats {
	return &SchedulerStats{
		ActiveProjects:   s.getActiveProjectCount(),
		ActiveWorkers:    s.getActiveWorkerCount(),
		TotalQueueDepth:  s.getTotalQueueDepth(),
		TasksDistributed: s.getTasksDistributedCount(),
		LastUpdate:       time.Now().Unix(),
	}
}

// SchedulerStats 调度器统计信息
type SchedulerStats struct {
	ActiveProjects   int   `json:"activeProjects"`
	ActiveWorkers    int   `json:"activeWorkers"`
	TotalQueueDepth  int64 `json:"totalQueueDepth"`
	TasksDistributed int64 `json:"tasksDistributed"`
	LastUpdate       int64 `json:"lastUpdate"`
}

// getActiveProjectCount 获取活跃项目数量
func (s *Scheduler) getActiveProjectCount() int {
	ctx := context.Background()
	projects, err := s.mongoDB.ListProjects(ctx)
	if err != nil {
		return 0
	}

	activeCount := 0
	for _, project := range projects {
		if project.Status == "active" {
			activeCount++
		}
	}
	return activeCount
}

// getActiveWorkerCount 获取活跃Worker数量
func (s *Scheduler) getActiveWorkerCount() int {
	ctx := context.Background()
	workers, err := s.mongoDB.ListWorkers(ctx)
	if err != nil {
		return 0
	}

	activeCount := 0
	for _, worker := range workers {
		if worker.Status == "active" && time.Since(worker.LastHeartbeat) < 2*time.Minute {
			activeCount++
		}
	}
	return activeCount
}

// getTotalQueueDepth 获取总队列深度
func (s *Scheduler) getTotalQueueDepth() int64 {
	ctx := context.Background()
	projects, err := s.mongoDB.ListProjects(ctx)
	if err != nil {
		return 0
	}

	totalDepth := int64(0)
	for _, project := range projects {
		if project.Status == "active" {
			depth, _ := s.redisClient.GetQueueDepth(ctx, project.ID.Hex())
			totalDepth += depth
		}
	}
	return totalDepth
}

// getTasksDistributedCount 获取已分发任务数量（这里需要实际的统计逻辑）
func (s *Scheduler) getTasksDistributedCount() int64 {
	// 这里应该从统计数据中获取
	return 0
}

// TaskDistributionStrategy 任务分发策略
type TaskDistributionStrategy int

const (
	StrategyRoundRobin TaskDistributionStrategy = iota
	StrategyLeastLoaded
	StrategyWeighted
)

// SetDistributionStrategy 设置分发策略
func (s *Scheduler) SetDistributionStrategy(strategy TaskDistributionStrategy) {
	// 实现分发策略设置
	log.Printf("Task distribution strategy set to: %d", strategy)
}
