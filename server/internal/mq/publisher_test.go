package mq

import (
	"context"
	"testing"
)

func TestPublisher_New(t *testing.T) {
	// 测试创建Publisher - 这个测试需要实际的RabbitMQ连接
	// 在没有RabbitMQ的情况下，我们只测试URL验证

	// 测试无效URL
	_, err := NewPublisher("invalid-url")
	if err == nil {
		t.Error("Expected error for invalid URL, got nil")
	}

	// 测试空URL
	_, err = NewPublisher("")
	if err == nil {
		t.Error("Expected error for empty URL, got nil")
	}
}

func TestPublisher_Publish(t *testing.T) {
	// 这个测试需要模拟或跳过，因为需要实际的RabbitMQ连接
	t.Skip("Skipping publish test - requires RabbitMQ connection")

	// 如果有RabbitMQ可用，可以使用以下代码：
	/*
		publisher, err := NewPublisher("amqp://guest:guest@localhost:5672/")
		if err != nil {
			t.<PERSON>("Skipping test - RabbitMQ not available: %v", err)
		}
		defer publisher.Close()

		ctx := context.Background()
		err = publisher.Publish(ctx, "test-queue", []byte("test message"))
		if err != nil {
			t.Errorf("Failed to publish message: %v", err)
		}
	*/
}

func TestPublisher_Close(t *testing.T) {
	// 测试Close方法不会panic
	publisher := &Publisher{}

	// 这应该不会panic，即使连接为nil
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("Close() panicked: %v", r)
		}
	}()

	publisher.Close()

	// 测试正常的Publisher关闭
	publisher = &Publisher{
		conn: nil,
		ch:   nil,
	}
	publisher.Close() // 这也不应该panic
}

// MockPublisher 用于测试的模拟Publisher
type MockPublisher struct {
	published [][]byte
}

func (m *MockPublisher) Publish(ctx context.Context, queueName string, body []byte) error {
	m.published = append(m.published, body)
	return nil
}

func (m *MockPublisher) Close() {
	// Mock实现，什么都不做
}

func TestMockPublisher(t *testing.T) {
	mock := &MockPublisher{}

	ctx := context.Background()
	testMsg := []byte("test message")

	err := mock.Publish(ctx, "test-queue", testMsg)
	if err != nil {
		t.Errorf("Mock publish failed: %v", err)
	}

	if len(mock.published) != 1 {
		t.Errorf("Expected 1 published message, got %d", len(mock.published))
	}

	if string(mock.published[0]) != "test message" {
		t.Errorf("Expected 'test message', got %s", string(mock.published[0]))
	}
}
