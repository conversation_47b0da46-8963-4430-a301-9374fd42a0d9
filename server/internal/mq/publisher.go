package mq

import (
	"context"
	"fmt"

	"github.com/rabbitmq/amqp091-go"
)

// Publisher handles sending messages to RabbitMQ.
type Publisher struct {
	conn *amqp091.Connection
	ch   *amqp091.Channel
}

// NewPublisher creates a new RabbitMQ publisher.
func NewPublisher(url string) (*Publisher, error) {
	conn, err := amqp091.Dial(url)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("failed to open a channel: %w", err)
	}

	return &Publisher{conn: conn, ch: ch}, nil
}

// Publish sends a message to a specific queue.
func (p *Publisher) Publish(ctx context.Context, queueName string, body []byte) error {
	q, err := p.ch.QueueDeclare(
		queueName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		map[string]interface{}{
			"x-message-ttl": ********, // 24小时过期，与Worker保持一致
		}, // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to declare a queue: %w", err)
	}

	return p.ch.PublishWithContext(ctx,
		"",     // exchange
		q.Name, // routing key
		false,  // mandatory
		false,  // immediate
		amqp091.Publishing{
			ContentType: "text/plain",
			Body:        body,
		})
}

// Close closes the publisher's channel and connection.
func (p *Publisher) Close() {
	if p.ch != nil {
		p.ch.Close()
	}
	if p.conn != nil {
		p.conn.Close()
	}
}
