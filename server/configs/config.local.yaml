# server/configs/config.yaml
server:
  port: 8080
  mode: debug
  
database:
  mongodb:
    uri: mongodb://developer:<EMAIL>:30017/?authSource=admin
    database: download_scheduler_test
    timeout: 10s
  redis:
    addr: redis-cn-outer.captcha-api.com:30079
    password: "FwMo9GQMupv5LRJp"
    db: 3
    
mq:
  rabbitmq:
    url: amqp://admin:<EMAIL>:30672/
    prefetch: 10
    
monitoring:
  enable: true
  stats_interval: 5s
  
log:
  level: info
  file: logs/server.log
