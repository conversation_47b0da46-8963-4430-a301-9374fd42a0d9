server:
  port: 8080
  mode: debug  # debug|release
  
database:
  mongodb:
    uri: *******************************************
    database: download_scheduler
    timeout: 10s
  redis:
    addr: localhost:6379
    password: "password123"
    db: 0
    
mq:
  rabbitmq:
    url: amqp://admin:password123@localhost:5672/
    prefetch: 10
    
monitoring:
  enable: true
  stats_interval: 5s
  
log:
  level: debug
  file: logs/server-test.log 