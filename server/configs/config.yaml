# Download Scheduler Server 默认配置文件
# 使用本地 Docker Compose 中间件环境

server:
  port: 8080
  mode: debug  # debug|release

database:
  mongodb:
    uri: *******************************************
    database: download_scheduler_dev
    timeout: 10s
  redis:
    addr: localhost:6379
    password: "password123"
    db: 0

mq:
  rabbitmq:
    url: amqp://admin:password123@localhost:5672/
    prefetch: 10

monitoring:
  enable: true
  stats_interval: 5s

log:
  level: debug
  file: logs/server.log
