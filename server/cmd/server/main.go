package main

import (
	"download-scheduler/server/internal/api"
	"download-scheduler/server/internal/config"
	"flag"
	"fmt"
	"log"

	"github.com/gin-gonic/gin"
)

func main() {
	// 解析命令行参数
	configPath := flag.String("config", "configs/config.yaml", "配置文件路径")
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load config: %s", err)
	}

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 初始化数据库连接
	err = api.InitDatabase()
	if err != nil {
		log.Fatalf("Failed to initialize database: %s", err)
	}

	// 初始化默认用户
	err = api.InitializeDefaultUsers()
	if err != nil {
		log.Fatalf("Failed to initialize default users: %s", err)
	}

	// 创建Gin路由
	r := gin.Default()

	// 注册API路由
	api.RegisterRoutes(r)

	// 健康检查接口
	r.GET("/ping", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "pong",
			"status":  "healthy",
		})
	})

	// 启动服务器
	addr := fmt.Sprintf(":%d", cfg.Server.Port)
	log.Printf("Server starting on %s", addr)
	if err := r.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %s", err)
	}
}
