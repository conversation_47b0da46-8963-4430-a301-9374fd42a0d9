# Dockerfile for the worker
FROM golang:1.23-alpine

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod ./
COPY go.sum ./

# Download all dependencies.
# Dependencies will be cached if the go.mod and go.sum files are not changed.
RUN go mod download

# Copy the source code
COPY . .

# Build the Go app
RUN go build -o ./out/worker ./cmd/worker

# Command to run the executable
CMD ["./out/worker"]
