# Go项目 Makefile

# 变量定义
BINARY_NAME=worker
MAIN_PATH=cmd/worker/main.go

# 默认目标
.DEFAULT_GOAL := help

# 开发环境运行
.PHONY: dev
dev:
	go run $(MAIN_PATH)

# 构建二进制文件
.PHONY: build
build:
	go build -o bin/$(BINARY_NAME) $(MAIN_PATH)

# 运行测试
.PHONY: test
test:
	go test ./...

# 代码格式化
.PHONY: fmt
fmt:
	go fmt ./...

# 代码检查
.PHONY: vet
vet:
	go vet ./...

# 下载依赖
.PHONY: deps
deps:
	go mod download
	go mod tidy

# 清理构建文件
.PHONY: clean
clean:
	rm -rf bin/
	go clean

# 显示帮助信息
.PHONY: help
help:
	@echo "可用的命令："
	@echo "  dev     - 开发环境运行服务器"
	@echo "  build   - 构建二进制文件"
	@echo "  test    - 运行测试"
	@echo "  fmt     - 格式化代码"
	@echo "  vet     - 代码检查"
	@echo "  deps    - 下载并整理依赖"
	@echo "  clean   - 清理构建文件"
	@echo "  help    - 显示此帮助信息"