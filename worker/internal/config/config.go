package config

import (
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// BasicConfig Worker 启动时的基本配置（本地）
type BasicConfig struct {
	ServerURL string `yaml:"server_url" json:"serverUrl"`
	Token     string `yaml:"token" json:"token"`
	WorkDir   string `yaml:"work_dir" json:"workDir"` // 本地工作目录
	TempDir   string `yaml:"temp_dir" json:"tempDir"` // 本地临时目录
}

// ServerConfig 由 Server 下发的完整配置
type ServerConfig struct {
	WorkerName        string         `json:"workerName"`        // Server 分配的名称
	HeartbeatInterval time.Duration  `json:"heartbeatInterval"` // 心跳间隔
	RabbitMQURL       string         `json:"rabbitmqUrl"`       // RabbitMQ 连接
	MaxConcurrent     int            `json:"maxConcurrent"`     // 最大并发任务数
	Download          DownloadConfig `json:"download"`          // 下载配置
	Packager          PackagerConfig `json:"packager"`          // 打包配置
	OSS               OSSConfig      `json:"oss"`               // OSS配置
	Log               LogConfig      `json:"log"`               // 日志配置
}

// Config 完整的 Worker 配置
type Config struct {
	Basic  BasicConfig  `yaml:"basic"`  // 基本配置（本地）
	Server ServerConfig `json:"server"` // 服务器配置（动态）
}

type WorkerConfig struct {
	Name              string        `yaml:"name"`
	ServerURL         string        `yaml:"server_url"`
	HeartbeatInterval time.Duration `yaml:"heartbeat_interval"`
	RabbitMQURL       string        `yaml:"rabbitmq_url"`
	MaxConcurrent     int           `yaml:"max_concurrent"`
	WorkDir           string        `yaml:"work_dir"`
	TempDir           string        `yaml:"temp_dir"`
}

type DownloadConfig struct {
	Concurrent  int           `yaml:"concurrent"`
	Timeout     time.Duration `yaml:"timeout"`
	Retry       int           `yaml:"retry"`
	BufferSize  int           `yaml:"buffer_size"`
	UserAgent   string        `yaml:"user_agent"`
	MaxFileSize int64         `yaml:"max_file_size"`
}

type PackagerConfig struct {
	TempDir      string `yaml:"temp_dir"`
	Compression  string `yaml:"compression"`
	MaxSizeGB    int    `yaml:"max_size_gb"`
	ChecksumType string `yaml:"checksum_type"`
}

type OSSConfig struct {
	UploadPartSize   int64         `yaml:"upload_part_size"`
	UploadConcurrent int           `yaml:"upload_concurrent"`
	Timeout          time.Duration `yaml:"timeout"`
}

type LogConfig struct {
	Level string `yaml:"level"`
	File  string `yaml:"file"`
}

var GlobalConfig *Config

// LoadBasicConfig 加载基本配置文件
func LoadBasicConfig(configPath string) (*BasicConfig, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var configFile struct {
		Basic BasicConfig `yaml:"basic"`
	}
	err = yaml.Unmarshal(data, &configFile)
	if err != nil {
		return nil, err
	}

	// 设置默认值
	setBasicDefaults(&configFile.Basic)
	return &configFile.Basic, nil
}

// LoadBasicFromEnv 从环境变量加载基本配置
func LoadBasicFromEnv() *BasicConfig {
	basicConfig := &BasicConfig{
		ServerURL: getEnv("SERVER_URL", "http://localhost:8080"),
		Token:     getEnv("WORKER_TOKEN", ""),
		WorkDir:   getEnv("WORK_DIR", "/data/work"),
		TempDir:   getEnv("TEMP_DIR", "/data/temp"),
	}

	setBasicDefaults(basicConfig)
	return basicConfig
}

// ApplyServerConfig 应用从 Server 获取的配置
func ApplyServerConfig(basic *BasicConfig, server *ServerConfig) *Config {
	config := &Config{
		Basic:  *basic,
		Server: *server,
	}

	// 设置服务器配置的默认值
	setServerDefaults(&config.Server)

	GlobalConfig = config
	return config
}

func setBasicDefaults(config *BasicConfig) {
	if config.WorkDir == "" {
		config.WorkDir = "/data/work"
	}
	if config.TempDir == "" {
		config.TempDir = "/data/temp"
	}
}

func setServerDefaults(config *ServerConfig) {
	if config.HeartbeatInterval == 0 {
		config.HeartbeatInterval = 5 * time.Second
	}
	if config.MaxConcurrent == 0 {
		config.MaxConcurrent = 5
	}
	if config.Download.Concurrent == 0 {
		config.Download.Concurrent = 3
	}
	if config.Download.Timeout == 0 {
		config.Download.Timeout = 300 * time.Second
	}
	if config.Download.Retry == 0 {
		config.Download.Retry = 3
	}
	if config.Download.BufferSize == 0 {
		config.Download.BufferSize = 1024 * 1024 // 1MB
	}
	if config.Download.UserAgent == "" {
		config.Download.UserAgent = "DownloadScheduler-Worker/1.0"
	}
	if config.Download.MaxFileSize == 0 {
		config.Download.MaxFileSize = 10 * 1024 * 1024 * 1024 // 10GB
	}
	if config.Packager.Compression == "" {
		config.Packager.Compression = "gzip"
	}
	if config.Packager.MaxSizeGB == 0 {
		config.Packager.MaxSizeGB = 10
	}
	if config.Packager.ChecksumType == "" {
		config.Packager.ChecksumType = "md5"
	}
	if config.OSS.UploadPartSize == 0 {
		config.OSS.UploadPartSize = 5 * 1024 * 1024 // 5MB
	}
	if config.OSS.UploadConcurrent == 0 {
		config.OSS.UploadConcurrent = 3
	}
	if config.OSS.Timeout == 0 {
		config.OSS.Timeout = 60 * time.Second
	}
	if config.Log.Level == "" {
		config.Log.Level = "info"
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// GetConfig 获取全局配置
func GetConfig() *Config {
	return GlobalConfig
}

// UpdateServerConfig 更新服务器配置
func UpdateServerConfig(server *ServerConfig) {
	if GlobalConfig != nil {
		setServerDefaults(server)
		GlobalConfig.Server = *server
	}
}
