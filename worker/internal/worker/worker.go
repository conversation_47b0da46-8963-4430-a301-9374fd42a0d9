package worker

import (
	"context"
	"fmt"
	"log"
	"os"
	"runtime"
	"strings"
	"sync"
	"syscall"
	"time"

	"download-scheduler/worker/internal/client"
	"download-scheduler/worker/internal/config"
	"download-scheduler/worker/internal/downloader"
)

// Worker 核心Worker结构
type Worker struct {
	config        *config.Config
	apiClient     *client.APIClient
	taskProcessor *downloader.TaskProcessor
	workerID      string
	token         string

	// 统计信息
	stats      *WorkerStats
	statsMutex sync.RWMutex

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// WorkerStats Worker统计信息
type WorkerStats struct {
	CPUUsage       float64
	MemoryUsage    float64
	DiskUsage      float64
	ActiveTasks    int
	TotalProcessed int64
	TotalFailed    int64
	StartTime      time.Time
	LastHeartbeat  time.Time
	LastError      string // 记录最后一次心跳失败的原因
}

// NewWorker 创建新的Worker
func NewWorker(basicCfg *config.BasicConfig) (*Worker, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建WebSocket API客户端
	apiClient := client.NewWebSocketAPIClient(basicCfg.ServerURL, basicCfg.Token)

	// 注意：WebSocket模式下，激活将通过WebSocket连接进行
	// 这里我们需要先建立连接，然后在连接回调中进行激活

	// 创建基础配置的Worker实例
	worker := &Worker{
		config:    &config.Config{}, // 临时空配置，将在WebSocket连接成功后更新
		apiClient: apiClient,
		token:     basicCfg.Token,
		ctx:       ctx,
		cancel:    cancel,
		stats: &WorkerStats{
			StartTime: time.Now(),
		},
	}

	// 设置WebSocket回调
	apiClient.SetWebSocketCallbacks(
		func() {
			log.Println("WebSocket connected, activating worker...")
			if err := worker.activateWorkerViaWebSocket(basicCfg); err != nil {
				log.Printf("Worker activation failed: %v", err)
			}
		},
		func() {
			log.Println("WebSocket disconnected")
		},
		func(messageType string, data interface{}) {
			worker.handleWebSocketMessage(messageType, data)
		},
	)

	return worker, nil
}

// Start 启动Worker
func (w *Worker) Start() error {
	log.Printf("Starting worker with WebSocket connection...")

	// 启动WebSocket连接
	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		if err := w.apiClient.RunWebSocket(); err != nil {
			log.Printf("WebSocket connection error: %v", err)
		}
	}()

	// 启动统计收集和发送循环
	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		w.statsAndWebSocketLoop()
	}()

	log.Printf("Worker started successfully with WebSocket")
	return nil
}

// Stop 停止Worker
func (w *Worker) Stop() {
	log.Println("Stopping worker...")
	w.cancel()
	if w.apiClient != nil {
		w.apiClient.StopWebSocket()
	}
	w.wg.Wait()
	if w.taskProcessor != nil {
		w.taskProcessor.Close()
	}
	log.Println("Worker stopped")
}

// statsAndWebSocketLoop 统计收集和WebSocket发送循环
func (w *Worker) statsAndWebSocketLoop() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒发送一次统计信息
	defer ticker.Stop()

	for {
		select {
		case <-w.ctx.Done():
			return
		case <-ticker.C:
			// 收集统计信息
			w.collectStats()

			// 如果WebSocket已连接，发送统计信息
			if w.apiClient.IsWebSocketConnected() {
				w.sendStatsViaWebSocket()
			}
		}
	}
}

// sendStatsViaWebSocket 通过WebSocket发送统计信息
func (w *Worker) sendStatsViaWebSocket() {
	w.statsMutex.RLock()
	stats := *w.stats
	w.statsMutex.RUnlock()

	// 计算网络速度（简化版，基于活跃任务数）
	networkSpeed := int64(stats.ActiveTasks * 1024 * 1024) // 估算网络使用

	// 创建健康状态
	health := client.WorkerHealth{
		Status: "healthy",
		Uptime: int64(time.Since(stats.StartTime).Seconds()),
	}

	// 如果资源使用率过高，设置为warning状态
	if stats.CPUUsage > 90 || stats.MemoryUsage > 90 || stats.DiskUsage > 90 {
		health.Status = "warning"
	}

	req := &client.WorkerHeartbeatRequest{
		Stats: client.WorkerStats{
			CPUUsage:       stats.CPUUsage,
			MemoryUsage:    stats.MemoryUsage,
			DiskUsage:      stats.DiskUsage,
			ActiveTasks:    stats.ActiveTasks,
			TotalProcessed: stats.TotalProcessed,
			TotalFailed:    stats.TotalFailed,
			NetworkSpeed:   networkSpeed,
		},
		Health: health,
	}

	err := w.apiClient.SendHeartbeat(req)
	if err != nil {
		log.Printf("Failed to send stats via WebSocket: %v", err)
		// 记录统计发送失败
		w.statsMutex.Lock()
		w.stats.LastError = fmt.Sprintf("Stats send failed: %v", err)
		w.statsMutex.Unlock()
	} else {
		// 更新最后心跳时间
		w.statsMutex.Lock()
		w.stats.LastHeartbeat = time.Now()
		w.stats.LastError = ""
		w.statsMutex.Unlock()
	}
}

// registerWorker 注册Worker到Server（已废弃，使用registerAndGetConfig）
func (w *Worker) registerWorker() error {
	// 这个方法已经被废弃，因为注册过程在NewWorker中完成
	// 这里只是为了兼容性保留，实际上不会被调用
	log.Println("Worker already registered during initialization")
	return nil
}

// collectStats 收集统计信息
func (w *Worker) collectStats() {
	// 收集系统统计信息
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 获取任务处理器统计（如果已初始化）
	var taskStats map[string]interface{}
	if w.taskProcessor != nil {
		taskStats = w.taskProcessor.GetStats()
	} else {
		// 如果taskProcessor还未初始化，返回默认值
		taskStats = map[string]interface{}{
			"active_tasks":    0,
			"total_processed": int64(0),
			"total_failed":    int64(0),
		}
	}

	w.statsMutex.Lock()
	defer w.statsMutex.Unlock()

	// 内存使用率（基于实际分配的内存）
	w.stats.MemoryUsage = float64(m.Alloc) / float64(m.Sys) * 100
	if w.stats.MemoryUsage > 100 {
		w.stats.MemoryUsage = 100
	}

	// CPU使用率（基于Go协程和活跃任务数的估算）
	numGoroutines := runtime.NumGoroutine()
	numCPU := runtime.NumCPU()

	// 简化的CPU使用率估算：基于协程数量和活跃任务
	activeTasks := 0
	if tasks, ok := taskStats["active_tasks"].(int); ok {
		activeTasks = tasks
	}

	// CPU使用率 = (协程数 + 活跃任务数 * 5) / CPU核心数 * 10
	// 这是一个简化的估算公式
	estimatedCPU := float64(numGoroutines+activeTasks*5) / float64(numCPU) * 10
	if estimatedCPU > 100 {
		estimatedCPU = 100
	}
	w.stats.CPUUsage = estimatedCPU

	// 磁盘使用率（检查工作目录的磁盘使用情况）
	diskUsage, err := w.calculateDiskUsage()
	if err != nil {
		log.Printf("Failed to calculate disk usage: %v", err)
		w.stats.DiskUsage = 0
	} else {
		w.stats.DiskUsage = diskUsage
	}

	// 任务统计
	if activeTasks, ok := taskStats["active_tasks"].(int); ok {
		w.stats.ActiveTasks = activeTasks
	}
	if totalProcessed, ok := taskStats["total_processed"].(int64); ok {
		w.stats.TotalProcessed = totalProcessed
	}
	if totalFailed, ok := taskStats["total_failed"].(int64); ok {
		w.stats.TotalFailed = totalFailed
	}
}

// calculateDiskUsage 计算磁盘使用率（优化版）
func (w *Worker) calculateDiskUsage() (float64, error) {
	workDir := w.config.Basic.WorkDir

	// 确保工作目录路径不为空，提供默认值
	if workDir == "" {
		workDir = "/tmp/download-scheduler/work"
		log.Printf("Work directory not configured, using default: %s", workDir)
	}

	// 确保工作目录存在，使用 os.MkdirAll 递归创建（相当于 mkdir -p）
	if _, err := os.Stat(workDir); os.IsNotExist(err) {
		log.Printf("Work directory does not exist, creating: %s", workDir)
		if err := os.MkdirAll(workDir, 0755); err != nil {
			return 0, fmt.Errorf("failed to create work directory %s: %w", workDir, err)
		}
		// 新创建的目录使用率为0
		return 0, nil
	}

	// 使用系统调用获取磁盘空间信息（更高效）
	var stat syscall.Statfs_t
	if err := syscall.Statfs(workDir, &stat); err != nil {
		// 如果系统调用失败，回退到简化计算
		log.Printf("Failed to get disk stats via syscall, using fallback method: %v", err)
		return w.calculateDiskUsageFallback(workDir)
	}

	// 计算磁盘使用率
	// Total blocks * block size = total space
	// Available blocks * block size = available space
	totalSpace := stat.Blocks * uint64(stat.Bsize)
	availableSpace := stat.Bavail * uint64(stat.Bsize)
	usedSpace := totalSpace - availableSpace

	if totalSpace == 0 {
		return 0, nil
	}

	usage := float64(usedSpace) / float64(totalSpace) * 100
	if usage > 100 {
		usage = 100
	}

	return usage, nil
}

// calculateDiskUsageFallback 磁盘使用率计算的回退方法
func (w *Worker) calculateDiskUsageFallback(workDir string) (float64, error) {
	// 只计算工作目录本身的大小，不递归遍历（避免大量文件时的性能问题）
	var workDirSize int64

	// 读取目录条目，只计算一级文件和目录的大小
	entries, err := os.ReadDir(workDir)
	if err != nil {
		return 0, fmt.Errorf("failed to read work directory: %w", err)
	}

	// 限制最多检查前1000个文件/目录，避免性能问题
	maxEntries := 1000
	for i, entry := range entries {
		if i >= maxEntries {
			log.Printf("Warning: Work directory has more than %d entries, disk usage calculation may be incomplete", maxEntries)
			break
		}

		info, err := entry.Info()
		if err != nil {
			continue // 忽略错误，继续计算
		}

		if info.IsDir() {
			// 对于目录，只获取目录本身的大小，不递归
			workDirSize += info.Size()
		} else {
			// 对于文件，获取文件大小
			workDirSize += info.Size()
		}
	}

	// 假设总磁盘空间为100GB（简化处理）
	// 在生产环境中可以通过配置文件设置或动态检测
	totalDiskSpace := int64(100 * 1024 * 1024 * 1024) // 100GB

	// 计算使用率
	usage := float64(workDirSize) / float64(totalDiskSpace) * 100
	if usage > 100 {
		usage = 100
	}

	return usage, nil
}

// GetStats 获取Worker统计信息
func (w *Worker) GetStats() WorkerStats {
	w.statsMutex.RLock()
	defer w.statsMutex.RUnlock()
	return *w.stats
}

// GetStatus 获取Worker状态
func (w *Worker) GetStatus() map[string]interface{} {
	stats := w.GetStats()
	return map[string]interface{}{
		"worker_id":       w.workerID,
		"status":          "running",
		"uptime":          time.Since(stats.StartTime).Seconds(),
		"cpu_usage":       stats.CPUUsage,
		"memory_usage":    stats.MemoryUsage,
		"disk_usage":      stats.DiskUsage,
		"active_tasks":    stats.ActiveTasks,
		"total_processed": stats.TotalProcessed,
		"total_failed":    stats.TotalFailed,
		"last_heartbeat":  stats.LastHeartbeat,
	}
}

// manageProjectConsumption 管理项目消费
func (w *Worker) manageProjectConsumption() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	consumingProjects := make(map[string]bool)

	for {
		select {
		case <-w.ctx.Done():
			return
		case <-ticker.C:
			// 获取分配的项目列表（简化版：直接从任务队列获取所有项目）
			// 在生产环境中，应该从Server API获取分配的项目列表
			projects := w.getAssignedProjects()

			for _, projectID := range projects {
				if !consumingProjects[projectID] {
					log.Printf("Starting to consume project: %s", projectID)
					consumingProjects[projectID] = true

					// 为每个项目启动消费协程
					w.wg.Add(1)
					go func(pid string) {
						defer w.wg.Done()
						defer func() {
							consumingProjects[pid] = false
						}()

						if err := w.taskProcessor.ConsumeProject(w.ctx, pid); err != nil {
							log.Printf("Failed to consume project %s: %v", pid, err)
						}
					}(projectID)
				}
			}
		}
	}
}

// getAssignedProjects 获取分配的项目（简化版实现）
func (w *Worker) getAssignedProjects() []string {
	log.Printf("[WORKER] Getting assigned projects...")

	// 我们可以通过环境变量来指定项目列表
	if projectIDs := os.Getenv("WORKER_PROJECT_IDS"); projectIDs != "" {
		// 支持逗号分隔的多个项目ID
		projects := strings.Split(projectIDs, ",")
		for i, project := range projects {
			projects[i] = strings.TrimSpace(project)
		}
		log.Printf("[WORKER] Found projects from environment: %v", projects)
		return projects
	}

	// 在生产环境中，这应该通过API从Server获取
	// 现在我们返回一个默认的项目列表用于测试
	defaultProjects := []string{"test_project"}
	log.Printf("[WORKER] Using default projects: %v", defaultProjects)
	return defaultProjects
}

// activateAndGetConfig 激活Worker并获取配置
func activateAndGetConfig(apiClient *client.APIClient, basicCfg *config.BasicConfig) (*config.ServerConfig, string, error) {
	log.Println("Activating worker on server...")

	// 获取主机信息
	hostname, _ := os.Hostname()
	if hostname == "" {
		hostname = "unknown"
	}

	req := &client.WorkerActivateRequest{
		Host:    "localhost", // 在实际部署中应该获取真实IP
		Port:    8081,
		Version: "1.0.0",
		SystemInfo: client.WorkerSystemInfo{
			OS:           runtime.GOOS,
			Arch:         runtime.GOARCH,
			CPUCores:     runtime.NumCPU(),
			MemoryGB:     8,    // 应该动态获取
			DiskSpaceGB:  100,  // 应该动态获取
			MaxBandwidth: 1000, // Mbps，应该配置或检测
		},
	}

	log.Printf("Sending activate request with token: %s, host: %s, port: %d",
		basicCfg.Token, req.Host, req.Port)

	resp, err := apiClient.ActivateWorker(req)
	var workerID string

	if err != nil {
		// 检查是否是409错误（worker已激活）
		if strings.Contains(err.Error(), "409") && strings.Contains(err.Error(), "Worker already activated") {
			log.Printf("Worker already activated, skipping activation step")
			// 继续获取配置
		} else {
			log.Printf("Activate worker failed: %v", err)
			return nil, "", err
		}
	} else {
		log.Printf("Worker activated successfully: %s, ID: %s", resp.Message, resp.WorkerID)
		workerID = resp.WorkerID
	}

	// 获取服务器配置
	serverConfig, err := apiClient.GetConfigUpdate()
	if err != nil {
		return nil, "", fmt.Errorf("failed to get server config: %w", err)
	}

	if serverConfig == nil {
		return nil, "", fmt.Errorf("server config is nil")
	}

	// 如果没有从activate响应中获得workerID，尝试从配置中获取
	if workerID == "" {
		workerID = serverConfig.WorkerName // 使用worker名称作为标识
	}

	// 转换配置格式
	cfg := &config.ServerConfig{
		WorkerName:        serverConfig.WorkerName,
		HeartbeatInterval: time.Duration(serverConfig.HeartbeatInterval) * time.Second,
		RabbitMQURL:       serverConfig.RabbitMQURL,
		MaxConcurrent:     serverConfig.MaxConcurrent,
		Download: config.DownloadConfig{
			Concurrent:  serverConfig.Download.Concurrent,
			Timeout:     time.Duration(serverConfig.Download.Timeout) * time.Second,
			Retry:       serverConfig.Download.Retry,
			BufferSize:  serverConfig.Download.BufferSize,
			UserAgent:   serverConfig.Download.UserAgent,
			MaxFileSize: serverConfig.Download.MaxFileSize,
		},
		Packager: config.PackagerConfig{
			TempDir:      serverConfig.Packager.TempDir,
			Compression:  serverConfig.Packager.Compression,
			MaxSizeGB:    serverConfig.Packager.MaxSizeGB,
			ChecksumType: serverConfig.Packager.ChecksumType,
		},
		OSS: config.OSSConfig{
			UploadPartSize:   serverConfig.OSS.UploadPartSize,
			UploadConcurrent: serverConfig.OSS.UploadConcurrent,
			Timeout:          time.Duration(serverConfig.OSS.Timeout) * time.Second,
		},
		Log: config.LogConfig{
			Level: serverConfig.Log.Level,
			File:  serverConfig.Log.File,
		},
	}

	return cfg, workerID, nil
}

// activateWorkerViaWebSocket 通过WebSocket激活Worker
func (w *Worker) activateWorkerViaWebSocket(basicCfg *config.BasicConfig) error {
	// 获取系统信息
	systemInfo := client.WorkerSystemInfo{
		OS:           runtime.GOOS,
		Arch:         runtime.GOARCH,
		CPUCores:     runtime.NumCPU(),
		MemoryGB:     8,    // 简化处理，实际应该获取真实内存
		DiskSpaceGB:  100,  // 简化处理
		MaxBandwidth: 1000, // 简化处理
	}

	activateReq := &client.WorkerActivateRequest{
		Host:       "localhost", // 可以从配置获取
		Port:       8080,        // 可以从配置获取
		Version:    "1.0.0",     // 可以从配置获取
		SystemInfo: systemInfo,
	}

	// 通过WebSocket发送激活请求
	_, err := w.apiClient.ActivateWorker(activateReq)
	if err != nil {
		return fmt.Errorf("failed to activate worker: %w", err)
	}

	log.Println("Worker activation request sent via WebSocket")
	return nil
}

// handleWebSocketMessage 处理WebSocket消息
func (w *Worker) handleWebSocketMessage(messageType string, data interface{}) {
	switch messageType {
	case "activate_ack":
		log.Println("Worker activation acknowledged")

		// 激活成功，可以开始初始化任务处理器等
		if err := w.initializeAfterActivation(); err != nil {
			log.Printf("Failed to initialize after activation: %v", err)
		}

	case "config":
		log.Println("Received configuration update")
		// 处理配置更新

	default:
		log.Printf("Unknown WebSocket message type: %s", messageType)
	}
}

// initializeAfterActivation 激活成功后的初始化
func (w *Worker) initializeAfterActivation() error {
	log.Println("Starting initialization after activation...")

	// 从服务器获取配置
	log.Println("Fetching configuration from server...")
	serverConfig, err := w.apiClient.GetConfigUpdate()
	if err != nil {
		return fmt.Errorf("failed to get server configuration: %w", err)
	}

	if serverConfig == nil {
		return fmt.Errorf("server configuration is nil")
	}

	log.Printf("Received server configuration: RabbitMQ URL = %s", serverConfig.RabbitMQURL)

	// 确保工作目录存在
	workDir := w.config.Basic.WorkDir
	if workDir == "" {
		workDir = "/tmp/download-scheduler/work"
	}
	if err := os.MkdirAll(workDir, 0755); err != nil {
		return fmt.Errorf("failed to create work directory %s: %w", workDir, err)
	}

	// 确保临时目录存在
	tempDir := w.config.Basic.TempDir
	if tempDir == "" {
		tempDir = "/tmp/download-scheduler/temp"
	}
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory %s: %w", tempDir, err)
	}

	// 构建完整配置
	fullConfig := &config.Config{
		Basic: config.BasicConfig{
			WorkDir:   workDir, // 使用确保存在的工作目录
			TempDir:   tempDir, // 使用确保存在的临时目录
			ServerURL: w.config.Basic.ServerURL,
			Token:     w.config.Basic.Token,
		},
		Server: config.ServerConfig{
			WorkerName:        serverConfig.WorkerName,
			HeartbeatInterval: time.Duration(serverConfig.HeartbeatInterval) * time.Second,
			RabbitMQURL:       serverConfig.RabbitMQURL, // 从服务器获取真正的RabbitMQ配置
			MaxConcurrent:     serverConfig.MaxConcurrent,
			Download: config.DownloadConfig{
				Concurrent:  serverConfig.Download.Concurrent,
				Timeout:     time.Duration(serverConfig.Download.Timeout) * time.Second,
				Retry:       serverConfig.Download.Retry,
				BufferSize:  serverConfig.Download.BufferSize,
				UserAgent:   serverConfig.Download.UserAgent,
				MaxFileSize: serverConfig.Download.MaxFileSize,
			},
			Packager: config.PackagerConfig{
				TempDir:      serverConfig.Packager.TempDir,
				Compression:  serverConfig.Packager.Compression,
				MaxSizeGB:    serverConfig.Packager.MaxSizeGB,
				ChecksumType: serverConfig.Packager.ChecksumType,
			},
			OSS: config.OSSConfig{
				UploadPartSize:   serverConfig.OSS.UploadPartSize,
				UploadConcurrent: serverConfig.OSS.UploadConcurrent,
				Timeout:          time.Duration(serverConfig.OSS.Timeout) * time.Second,
			},
			Log: config.LogConfig{
				Level: serverConfig.Log.Level,
				File:  serverConfig.Log.File,
			},
		},
	}

	// 更新worker配置
	w.config = fullConfig

	// 创建TaskProcessor
	w.taskProcessor, err = downloader.NewTaskProcessor(w.config)
	if err != nil {
		return fmt.Errorf("failed to create task processor: %w", err)
	}

	// 启动TaskProcessor
	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		if err := w.taskProcessor.Start(w.ctx); err != nil {
			log.Printf("Task processor failed: %v", err)
		}
	}()

	// 启动项目消费管理
	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		w.manageProjectConsumption()
	}()

	log.Println("Worker initialized successfully after activation")
	return nil
}
