package uploader

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"

	"download-scheduler/worker/internal/config"
)

// OSSConfig OSS配置
type OSSConfig struct {
	Provider       string `json:"provider"` // aliyun|aws|minio
	Endpoint       string `json:"endpoint"`
	Bucket         string `json:"bucket"`
	AccessKey      string `json:"accessKey"`
	SecretKey      string `json:"secretKey"`
	Prefix         string `json:"prefix"`
	Region         string `json:"region"`
	ForcePathStyle bool   `json:"forcePathStyle"` // 强制使用路径样式，MinIO需要此选项
}

// Uploader 文件上传器
type Uploader struct {
	config    *config.OSSConfig
	ossConfig *OSSConfig
}

// NewUploader 创建新的上传器
func NewUploader(cfg *config.OSSConfig, ossCfg *OSSConfig) *Uploader {
	return &Uploader{
		config:    cfg,
		ossConfig: ossCfg,
	}
}

// UploadResult 上传结果
type UploadResult struct {
	LocalPath   string        `json:"localPath"`
	OSSPath     string        `json:"ossPath"`
	Size        int64         `json:"size"`
	Duration    time.Duration `json:"duration"`
	UploadSpeed int64         `json:"uploadSpeed"` // bytes/second
	Success     bool          `json:"success"`
	Error       string        `json:"error,omitempty"`
}

// UploadFile 上传文件到OSS
func (u *Uploader) UploadFile(ctx context.Context, localPath, ossKey string) *UploadResult {
	start := time.Now()
	result := &UploadResult{
		LocalPath: localPath,
		OSSPath:   ossKey,
	}

	log.Printf("[UPLOADER] Starting upload: %s -> %s", localPath, ossKey)
	log.Printf("[UPLOADER] OSS Provider: %s", u.ossConfig.Provider)

	// 获取文件信息
	fileInfo, err := os.Stat(localPath)
	if err != nil {
		result.Error = fmt.Sprintf("stat file: %v", err)
		log.Printf("[UPLOADER] Failed to stat file %s: %v", localPath, err)
		return result
	}
	result.Size = fileInfo.Size()
	log.Printf("[UPLOADER] File size: %d bytes", result.Size)

	// 根据provider选择上传方式
	switch strings.ToLower(u.ossConfig.Provider) {
	case "aliyun":
		log.Printf("[UPLOADER] Using Aliyun OSS provider")
		err = u.uploadToAliyun(ctx, localPath, ossKey)
	case "minio":
		log.Printf("[UPLOADER] Using MinIO provider")
		err = u.uploadToMinio(ctx, localPath, ossKey)
	case "aws":
		log.Printf("[UPLOADER] AWS S3 provider requested but not implemented")
		err = fmt.Errorf("AWS S3 upload not implemented yet")
	default:
		log.Printf("[UPLOADER] Unsupported OSS provider: %s", u.ossConfig.Provider)
		err = fmt.Errorf("unsupported OSS provider: %s", u.ossConfig.Provider)
	}

	if err != nil {
		result.Error = fmt.Sprintf("upload failed: %v", err)
		log.Printf("[UPLOADER] Upload failed: %v", err)
		return result
	}

	result.Duration = time.Since(start)
	result.Success = true

	// 计算上传速度
	if result.Duration.Seconds() > 0 {
		result.UploadSpeed = int64(float64(result.Size) / result.Duration.Seconds())
	}

	log.Printf("[UPLOADER] Upload completed successfully: %s, size: %d bytes, duration: %v, speed: %d bytes/s",
		ossKey, result.Size, result.Duration, result.UploadSpeed)
	return result
}

// uploadToAliyun 上传到阿里云OSS
func (u *Uploader) uploadToAliyun(ctx context.Context, localPath, ossKey string) error {
	log.Printf("[UPLOADER] Creating Aliyun OSS client, endpoint: %s, bucket: %s", u.ossConfig.Endpoint, u.ossConfig.Bucket)

	// 创建OSS客户端
	client, err := oss.New(u.ossConfig.Endpoint, u.ossConfig.AccessKey, u.ossConfig.SecretKey)
	if err != nil {
		log.Printf("[UPLOADER] Failed to create Aliyun OSS client: %v", err)
		return fmt.Errorf("create aliyun client: %w", err)
	}
	log.Printf("[UPLOADER] Aliyun OSS client created successfully")

	// 获取bucket
	bucket, err := client.Bucket(u.ossConfig.Bucket)
	if err != nil {
		log.Printf("[UPLOADER] Failed to get bucket %s: %v", u.ossConfig.Bucket, err)
		return fmt.Errorf("get bucket: %w", err)
	}
	log.Printf("[UPLOADER] Bucket %s accessed successfully", u.ossConfig.Bucket)

	// 构建完整的OSS key
	originalKey := ossKey
	if u.ossConfig.Prefix != "" {
		ossKey = u.ossConfig.Prefix + ossKey
		log.Printf("[UPLOADER] OSS key with prefix: %s", ossKey)
	}

	// 分片上传（大文件）
	fileInfo, _ := os.Stat(localPath)
	if fileInfo.Size() > u.config.UploadPartSize {
		log.Printf("[UPLOADER] File size %d bytes exceeds part size %d, using multipart upload", fileInfo.Size(), u.config.UploadPartSize)
		return u.multipartUploadAliyun(bucket, localPath, ossKey)
	}

	// 普通上传
	log.Printf("[UPLOADER] Using simple upload for file: %s", originalKey)
	err = bucket.PutObjectFromFile(ossKey, localPath)
	if err != nil {
		log.Printf("[UPLOADER] Simple upload failed: %v", err)
		return err
	}
	log.Printf("[UPLOADER] Simple upload completed successfully")
	return nil
}

// multipartUploadAliyun 阿里云分片上传
func (u *Uploader) multipartUploadAliyun(bucket *oss.Bucket, localPath, ossKey string) error {
	// 初始化分片上传
	imur, err := bucket.InitiateMultipartUpload(ossKey)
	if err != nil {
		return fmt.Errorf("initiate multipart upload: %w", err)
	}

	// 打开文件
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("open file: %w", err)
	}
	defer file.Close()

	var parts []oss.UploadPart
	partNumber := 1
	buffer := make([]byte, u.config.UploadPartSize)

	for {
		n, err := file.Read(buffer)
		if n == 0 {
			break
		}

		// 上传分片
		part, uploadErr := bucket.UploadPart(imur, bytes.NewReader(buffer[:n]), int64(n), partNumber)
		if uploadErr != nil {
			bucket.AbortMultipartUpload(imur)
			return fmt.Errorf("upload part %d: %w", partNumber, uploadErr)
		}
		parts = append(parts, part)
		partNumber++

		if err != nil && err != io.EOF {
			bucket.AbortMultipartUpload(imur)
			return fmt.Errorf("read file: %w", err)
		}
		if err == io.EOF {
			break
		}
	}

	// 完成分片上传
	_, err = bucket.CompleteMultipartUpload(imur, parts)
	if err != nil {
		return fmt.Errorf("complete multipart upload: %w", err)
	}

	return nil
}

// uploadToMinio 上传到MinIO
func (u *Uploader) uploadToMinio(ctx context.Context, localPath, ossKey string) error {
	// 创建MinIO客户端
	useSSL := strings.HasPrefix(u.ossConfig.Endpoint, "https://")
	endpoint := strings.TrimPrefix(u.ossConfig.Endpoint, "https://")
	endpoint = strings.TrimPrefix(endpoint, "http://")

	client, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(u.ossConfig.AccessKey, u.ossConfig.SecretKey, ""),
		Secure: useSSL,
		// 使用 ForcePathStyle 配置
		Region:       u.ossConfig.Region,
		BucketLookup: minio.BucketLookupAuto,
	})
	if err != nil {
		return fmt.Errorf("create minio client: %w", err)
	}

	// 如果配置了强制路径样式，设置对应的选项
	if u.ossConfig.ForcePathStyle {
		// MinIO客户端默认使用路径样式，这里只是确保正确处理
		// 实际上 minio.BucketLookupPath 对应强制路径样式
		client, err = minio.New(endpoint, &minio.Options{
			Creds:        credentials.NewStaticV4(u.ossConfig.AccessKey, u.ossConfig.SecretKey, ""),
			Secure:       useSSL,
			Region:       u.ossConfig.Region,
			BucketLookup: minio.BucketLookupPath,
		})
		if err != nil {
			return fmt.Errorf("create minio client with path style: %w", err)
		}
	}

	// 构建完整的OSS key
	if u.ossConfig.Prefix != "" {
		ossKey = u.ossConfig.Prefix + ossKey
	}

	// 上传文件
	_, err = client.FPutObject(ctx, u.ossConfig.Bucket, ossKey, localPath, minio.PutObjectOptions{
		ContentType: "application/octet-stream",
	})
	if err != nil {
		return fmt.Errorf("minio upload: %w", err)
	}

	return nil
}

// DeleteLocalFile 删除本地文件
func (u *Uploader) DeleteLocalFile(localPath string) error {
	return os.Remove(localPath)
}

// ValidateUpload 验证上传结果
func (u *Uploader) ValidateUpload(ctx context.Context, ossKey string) error {
	switch strings.ToLower(u.ossConfig.Provider) {
	case "aliyun":
		return u.validateAliyunUpload(ossKey)
	case "minio":
		return u.validateMinioUpload(ctx, ossKey)
	default:
		return fmt.Errorf("validation not implemented for provider: %s", u.ossConfig.Provider)
	}
}

// validateAliyunUpload 验证阿里云上传
func (u *Uploader) validateAliyunUpload(ossKey string) error {
	client, err := oss.New(u.ossConfig.Endpoint, u.ossConfig.AccessKey, u.ossConfig.SecretKey)
	if err != nil {
		return err
	}

	bucket, err := client.Bucket(u.ossConfig.Bucket)
	if err != nil {
		return err
	}

	if u.ossConfig.Prefix != "" {
		ossKey = u.ossConfig.Prefix + ossKey
	}

	exist, err := bucket.IsObjectExist(ossKey)
	if err != nil {
		return err
	}

	if !exist {
		return fmt.Errorf("object not found in OSS: %s", ossKey)
	}

	return nil
}

// validateMinioUpload 验证MinIO上传
func (u *Uploader) validateMinioUpload(ctx context.Context, ossKey string) error {
	useSSL := strings.HasPrefix(u.ossConfig.Endpoint, "https://")
	endpoint := strings.TrimPrefix(u.ossConfig.Endpoint, "https://")
	endpoint = strings.TrimPrefix(endpoint, "http://")

	options := &minio.Options{
		Creds:        credentials.NewStaticV4(u.ossConfig.AccessKey, u.ossConfig.SecretKey, ""),
		Secure:       useSSL,
		Region:       u.ossConfig.Region,
		BucketLookup: minio.BucketLookupAuto,
	}

	// 如果配置了强制路径样式
	if u.ossConfig.ForcePathStyle {
		options.BucketLookup = minio.BucketLookupPath
	}

	client, err := minio.New(endpoint, options)
	if err != nil {
		return err
	}

	if u.ossConfig.Prefix != "" {
		ossKey = u.ossConfig.Prefix + ossKey
	}

	_, err = client.StatObject(ctx, u.ossConfig.Bucket, ossKey, minio.StatObjectOptions{})
	if err != nil {
		return fmt.Errorf("object not found in OSS: %s", ossKey)
	}

	return nil
}

// GenerateOSSKey 生成OSS存储路径
func (u *Uploader) GenerateOSSKey(projectID, batchID, fileName string) string {
	timestamp := time.Now().Format("2006-01-02")
	return filepath.Join(projectID, timestamp, batchID, fileName)
}

// GetUploadProgress 获取上传进度（简化版）
func (u *Uploader) GetUploadProgress() map[string]interface{} {
	return map[string]interface{}{
		"active_uploads": 0,
		"total_uploaded": 0,
		"total_failed":   0,
		"upload_speed":   0,
	}
}
