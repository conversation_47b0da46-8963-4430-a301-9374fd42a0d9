package client

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// WebSocketClient WebSocket客户端
type WebSocketClient struct {
	baseURL    string
	token      string
	conn       *websocket.Conn
	connected  bool
	ctx        context.Context
	cancel     context.CancelFunc
	reconnectC chan struct{}
	sendC      chan []byte
	statsC     chan StatsData
	mutex      sync.RWMutex

	// 回调函数
	onConnected    func()
	onDisconnected func()
	onMessage      func(messageType string, data interface{})
}

// StatsData Worker统计数据
type StatsData struct {
	Stats  WorkerStats  `json:"stats"`
	Health WorkerHealth `json:"health"`
}

// Message WebSocket消息格式
type Message struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

// NewWebSocketClient 创建WebSocket客户端
func NewWebSocketClient(baseURL, token string) *WebSocketClient {
	ctx, cancel := context.WithCancel(context.Background())

	return &WebSocketClient{
		baseURL:    baseURL,
		token:      token,
		ctx:        ctx,
		cancel:     cancel,
		reconnectC: make(chan struct{}, 1),
		sendC:      make(chan []byte, 256),
		statsC:     make(chan StatsData, 10),
	}
}

// SetCallbacks 设置回调函数
func (wsc *WebSocketClient) SetCallbacks(
	onConnected func(),
	onDisconnected func(),
	onMessage func(messageType string, data interface{}),
) {
	wsc.onConnected = onConnected
	wsc.onDisconnected = onDisconnected
	wsc.onMessage = onMessage
}

// Connect 连接到WebSocket服务器
func (wsc *WebSocketClient) Connect() error {
	// 构建WebSocket URL
	u, err := url.Parse(wsc.baseURL)
	if err != nil {
		return fmt.Errorf("invalid base URL: %w", err)
	}

	// 将HTTP(S)协议改为WS(S)
	switch u.Scheme {
	case "http":
		u.Scheme = "ws"
	case "https":
		u.Scheme = "wss"
	default:
		return fmt.Errorf("unsupported scheme: %s", u.Scheme)
	}

	u.Path = "/api/workers/connect"

	// 设置请求头
	headers := http.Header{}
	headers.Set("Authorization", "Bearer "+wsc.token)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), headers)
	if err != nil {
		return fmt.Errorf("failed to connect WebSocket: %w", err)
	}

	wsc.mutex.Lock()
	wsc.conn = conn
	wsc.connected = true
	wsc.mutex.Unlock()

	log.Printf("WebSocket connected to %s", u.String())

	// 启动读写协程
	go wsc.readPump()
	go wsc.writePump()
	go wsc.statsLoop()

	// 触发连接回调
	if wsc.onConnected != nil {
		wsc.onConnected()
	}

	return nil
}

// Disconnect 断开WebSocket连接
func (wsc *WebSocketClient) Disconnect() {
	wsc.mutex.Lock()
	if wsc.conn != nil {
		wsc.conn.Close()
	}
	wsc.connected = false
	wsc.mutex.Unlock()

	wsc.cancel()
}

// IsConnected 检查是否已连接
func (wsc *WebSocketClient) IsConnected() bool {
	wsc.mutex.RLock()
	defer wsc.mutex.RUnlock()
	return wsc.connected
}

// SendActivate 发送激活消息
func (wsc *WebSocketClient) SendActivate(req *WorkerActivateRequest) error {
	msg := Message{
		Type: "activate",
		Data: req,
	}

	return wsc.sendMessage(msg)
}

// SendStats 发送统计信息
func (wsc *WebSocketClient) SendStats(stats StatsData) error {
	// 通过通道发送，避免阻塞
	select {
	case wsc.statsC <- stats:
		return nil
	default:
		return fmt.Errorf("stats channel full")
	}
}

// sendMessage 发送消息
func (wsc *WebSocketClient) sendMessage(msg Message) error {
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("marshal message: %w", err)
	}

	select {
	case wsc.sendC <- data:
		return nil
	case <-wsc.ctx.Done():
		return wsc.ctx.Err()
	default:
		return fmt.Errorf("send channel full")
	}
}

// readPump 读取消息
func (wsc *WebSocketClient) readPump() {
	defer func() {
		wsc.handleDisconnect()
	}()

	wsc.conn.SetReadLimit(512)
	wsc.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	wsc.conn.SetPongHandler(func(string) error {
		wsc.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		select {
		case <-wsc.ctx.Done():
			return
		default:
		}

		_, messageBytes, err := wsc.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket read error: %v", err)
			}
			return
		}

		// 解析消息
		var msg Message
		if err := json.Unmarshal(messageBytes, &msg); err != nil {
			log.Printf("Failed to unmarshal message: %v", err)
			continue
		}

		// 处理消息
		wsc.handleMessage(msg)
	}
}

// writePump 写入消息
func (wsc *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		wsc.conn.Close()
	}()

	for {
		select {
		case <-wsc.ctx.Done():
			return

		case message := <-wsc.sendC:
			wsc.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := wsc.conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("WebSocket write error: %v", err)
				return
			}

		case <-ticker.C:
			wsc.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := wsc.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// statsLoop 统计信息发送循环
func (wsc *WebSocketClient) statsLoop() {
	for {
		select {
		case <-wsc.ctx.Done():
			return

		case stats := <-wsc.statsC:
			msg := Message{
				Type: "stats",
				Data: stats,
			}

			data, err := json.Marshal(msg)
			if err != nil {
				log.Printf("Failed to marshal stats: %v", err)
				continue
			}

			select {
			case wsc.sendC <- data:
			default:
				log.Printf("Failed to send stats: channel full")
			}
		}
	}
}

// handleMessage 处理接收到的消息
func (wsc *WebSocketClient) handleMessage(msg Message) {
	switch msg.Type {
	case "activate_ack":
		log.Printf("Worker activation acknowledged: %v", msg.Data)

	case "pong":
		// Pong响应，更新心跳时间

	case "config":
		log.Printf("Received config update: %v", msg.Data)

	default:
		log.Printf("Unknown message type: %s", msg.Type)
	}

	// 触发消息回调
	if wsc.onMessage != nil {
		wsc.onMessage(msg.Type, msg.Data)
	}
}

// handleDisconnect 处理断开连接
func (wsc *WebSocketClient) handleDisconnect() {
	wsc.mutex.Lock()
	wsc.connected = false
	if wsc.conn != nil {
		wsc.conn.Close()
		wsc.conn = nil
	}
	wsc.mutex.Unlock()

	log.Printf("WebSocket disconnected")

	// 触发断开连接回调
	if wsc.onDisconnected != nil {
		wsc.onDisconnected()
	}

	// 请求重连
	select {
	case wsc.reconnectC <- struct{}{}:
	default:
	}
}

// StartAutoReconnect 启动自动重连
func (wsc *WebSocketClient) StartAutoReconnect() {
	go func() {
		for {
			select {
			case <-wsc.ctx.Done():
				return

			case <-wsc.reconnectC:
				if !wsc.IsConnected() {
					log.Printf("Attempting to reconnect...")

					// 重连延迟
					time.Sleep(5 * time.Second)

					// 尝试重连
					if err := wsc.Connect(); err != nil {
						log.Printf("Reconnect failed: %v", err)

						// 触发下一次重连
						select {
						case wsc.reconnectC <- struct{}{}:
						default:
						}
					}
				}
			}
		}
	}()
}

// Run 运行WebSocket客户端（阻塞）
func (wsc *WebSocketClient) Run() error {
	// 启动自动重连
	wsc.StartAutoReconnect()

	// 初始连接
	if err := wsc.Connect(); err != nil {
		return fmt.Errorf("initial connection failed: %w", err)
	}

	// 等待上下文取消
	<-wsc.ctx.Done()
	return wsc.ctx.Err()
}
