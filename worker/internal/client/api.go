package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// APIClient Server API客户端
type APIClient struct {
	baseURL     string
	httpClient  *http.Client
	token       string
	wsClient    *WebSocketClient
	isWebSocket bool
}

// NewAPIClient 创建新的API客户端
func NewAPIClient(baseURL string) *APIClient {
	return &APIClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		isWebSocket: false,
	}
}

// NewWebSocketAPIClient 创建WebSocket API客户端
func NewWebSocketAPIClient(baseURL, token string) *APIClient {
	client := &APIClient{
		baseURL:     baseURL,
		httpClient:  &http.Client{Timeout: 30 * time.Second}, // 初始化HTTP客户端用于备用操作
		token:       token,
		isWebSocket: true,
	}

	// 创建WebSocket客户端
	client.wsClient = NewWebSocketClient(baseURL, token)

	return client
}

// SetToken 设置认证Token
func (c *APIClient) SetToken(token string) {
	c.token = token
	if c.wsClient != nil {
		c.wsClient.token = token
	}
}

// StartWebSocket 启动WebSocket连接
func (c *APIClient) StartWebSocket() error {
	if !c.isWebSocket || c.wsClient == nil {
		return fmt.Errorf("not a WebSocket client")
	}

	return c.wsClient.Connect()
}

// RunWebSocket 运行WebSocket客户端（阻塞）
func (c *APIClient) RunWebSocket() error {
	if !c.isWebSocket || c.wsClient == nil {
		return fmt.Errorf("not a WebSocket client")
	}

	return c.wsClient.Run()
}

// StopWebSocket 停止WebSocket连接
func (c *APIClient) StopWebSocket() {
	if c.wsClient != nil {
		c.wsClient.Disconnect()
	}
}

// IsWebSocketConnected 检查WebSocket是否已连接
func (c *APIClient) IsWebSocketConnected() bool {
	if c.wsClient == nil {
		return false
	}
	return c.wsClient.IsConnected()
}

// SetWebSocketCallbacks 设置WebSocket回调函数
func (c *APIClient) SetWebSocketCallbacks(
	onConnected func(),
	onDisconnected func(),
	onMessage func(messageType string, data interface{}),
) {
	if c.wsClient != nil {
		c.wsClient.SetCallbacks(onConnected, onDisconnected, onMessage)
	}
}

// WorkerActivateRequest Worker激活请求（与server匹配）
type WorkerActivateRequest struct {
	Host       string           `json:"host" binding:"required"`
	Port       int              `json:"port" binding:"required"`
	Version    string           `json:"version" binding:"required"`
	SystemInfo WorkerSystemInfo `json:"systemInfo" binding:"required"`
}

// WorkerSystemInfo Worker系统信息
type WorkerSystemInfo struct {
	OS           string `json:"os"`
	Arch         string `json:"arch"`
	CPUCores     int    `json:"cpuCores"`
	MemoryGB     int    `json:"memoryGB"`
	DiskSpaceGB  int    `json:"diskSpaceGB"`
	MaxBandwidth int    `json:"maxBandwidth"` // Mbps
}

// WorkerActivateResponse Worker激活响应（包含配置）
type WorkerActivateResponse struct {
	Message          string   `json:"message"`
	WorkerID         string   `json:"workerId"`
	AssignedProjects []string `json:"assignedProjects"`
}

// WorkerServerConfig Server下发的Worker配置
type WorkerServerConfig struct {
	WorkerName        string         `json:"workerName"`        // Server分配的名称
	HeartbeatInterval int            `json:"heartbeatInterval"` // 心跳间隔（秒）
	RabbitMQURL       string         `json:"rabbitmqUrl"`       // RabbitMQ连接
	MaxConcurrent     int            `json:"maxConcurrent"`     // 最大并发任务数
	Download          DownloadConfig `json:"download"`          // 下载配置
	Packager          PackagerConfig `json:"packager"`          // 打包配置
	OSS               OSSConfig      `json:"oss"`               // OSS配置
	Log               LogConfig      `json:"log"`               // 日志配置
}

// 配置结构体定义
type DownloadConfig struct {
	Concurrent  int    `json:"concurrent"`
	Timeout     int    `json:"timeout"` // 秒
	Retry       int    `json:"retry"`
	BufferSize  int    `json:"bufferSize"`
	UserAgent   string `json:"userAgent"`
	MaxFileSize int64  `json:"maxFileSize"`
}

type PackagerConfig struct {
	TempDir      string `json:"tempDir"`
	Compression  string `json:"compression"`
	MaxSizeGB    int    `json:"maxSizeGb"`
	ChecksumType string `json:"checksumType"`
}

type OSSConfig struct {
	UploadPartSize   int64 `json:"uploadPartSize"`
	UploadConcurrent int   `json:"uploadConcurrent"`
	Timeout          int   `json:"timeout"`        // 秒
	ForcePathStyle   bool  `json:"forcePathStyle"` // 强制使用路径样式，MinIO需要此选项
}

type LogConfig struct {
	Level string `json:"level"`
	File  string `json:"file"`
}

// WorkerStats Worker统计信息
type WorkerStats struct {
	CPUUsage       float64 `json:"cpuUsage"`
	MemoryUsage    float64 `json:"memoryUsage"`
	DiskUsage      float64 `json:"diskUsage"`
	ActiveTasks    int     `json:"activeTasks"`
	TotalProcessed int64   `json:"totalProcessed"`
	TotalFailed    int64   `json:"totalFailed"`
	NetworkSpeed   int64   `json:"networkSpeed"` // 网络速度(字节/秒)
}

// WorkerHealth Worker健康状态
type WorkerHealth struct {
	Status    string `json:"status"`    // healthy|warning|error
	Uptime    int64  `json:"uptime"`    // 运行时间(秒)
	LastError string `json:"lastError"` // 最后错误信息
}

// WorkerHeartbeatRequest Worker心跳请求
type WorkerHeartbeatRequest struct {
	Stats  WorkerStats  `json:"stats"`
	Health WorkerHealth `json:"health"`
}

// ActivateWorker 激活Worker
func (c *APIClient) ActivateWorker(req *WorkerActivateRequest) (*WorkerActivateResponse, error) {
	if c.isWebSocket && c.wsClient != nil {
		// 通过WebSocket发送激活消息
		return nil, c.wsClient.SendActivate(req)
	}

	// 传统HTTP方式
	data, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request: %w", err)
	}

	resp, err := c.doRequest("POST", "/api/workers/activate", data, true)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容用于详细错误信息
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("activate failed with status: %d, response: %s", resp.StatusCode, string(bodyBytes))
	}

	var result WorkerActivateResponse
	if err := json.Unmarshal(bodyBytes, &result); err != nil {
		return nil, fmt.Errorf("decode response: %w", err)
	}

	return &result, nil
}

// SendHeartbeat 发送心跳
func (c *APIClient) SendHeartbeat(req *WorkerHeartbeatRequest) error {
	if c.isWebSocket && c.wsClient != nil {
		// 通过WebSocket发送统计信息
		stats := StatsData{
			Stats:  req.Stats,
			Health: req.Health,
		}
		return c.wsClient.SendStats(stats)
	}

	// 传统HTTP方式
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("marshal heartbeat: %w", err)
	}

	resp, err := c.doRequest("POST", "/api/workers/heartbeat", data, true)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("heartbeat failed with status: %d", resp.StatusCode)
	}

	return nil
}

// GetProjectControl 获取项目控制状态
func (c *APIClient) GetProjectControl(projectID string) (string, error) {
	url := fmt.Sprintf("/api/projects/%s", projectID)
	resp, err := c.doRequest("GET", url, nil, false)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("get project failed with status: %d", resp.StatusCode)
	}

	var project struct {
		Status string `json:"status"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&project); err != nil {
		return "", fmt.Errorf("decode project: %w", err)
	}

	return project.Status, nil
}

// doRequest 执行HTTP请求
func (c *APIClient) doRequest(method, path string, body []byte, needAuth bool) (*http.Response, error) {
	url := c.baseURL + path

	var bodyReader io.Reader
	if body != nil {
		bodyReader = bytes.NewReader(body)
	}

	req, err := http.NewRequest(method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	if needAuth && c.token != "" {
		req.Header.Set("Authorization", "Bearer "+c.token)
	}

	return c.httpClient.Do(req)
}

// GetConfigUpdate 获取配置更新
func (c *APIClient) GetConfigUpdate() (*WorkerServerConfig, error) {
	resp, err := c.doRequest("GET", "/api/workers/config", nil, true)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotModified {
		return nil, nil // 配置未更新
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get config failed with status: %d", resp.StatusCode)
	}

	var config WorkerServerConfig
	if err := json.NewDecoder(resp.Body).Decode(&config); err != nil {
		return nil, fmt.Errorf("decode config: %w", err)
	}

	return &config, nil
}

// HealthCheck 健康检查
func (c *APIClient) HealthCheck() error {
	resp, err := c.doRequest("GET", "/health", nil, false)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed with status: %d", resp.StatusCode)
	}

	return nil
}
