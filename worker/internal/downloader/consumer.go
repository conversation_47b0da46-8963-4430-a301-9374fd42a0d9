package downloader

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/rabbitmq/amqp091-go"

	"download-scheduler/worker/internal/config"
	"download-scheduler/worker/internal/packager"
	"download-scheduler/worker/internal/uploader"
)

// TaskMessage 任务消息
type TaskMessage struct {
	ID        string              `json:"id"`
	ProjectID string              `json:"projectId"`
	BatchID   string              `json:"batchId"`
	URL       string              `json:"url"`
	Priority  int                 `json:"priority"`
	OSSConfig *uploader.OSSConfig `json:"ossConfig"`
}

// TaskProcessor 任务处理器
type TaskProcessor struct {
	conn       *amqp091.Connection
	ch         *amqp091.Channel
	config     *config.Config
	downloader *Downloader
	packager   *packager.Packager
	uploader   *uploader.Uploader

	// 项目数据缓存
	projectData map[string]*ProjectData
	mutex       sync.RWMutex

	// 统计信息
	stats *ProcessorStats
}

// ProjectData 项目数据
type ProjectData struct {
	ProjectID string
	BatchID   string
	Files     []string
	TotalSize int64
	OSSConfig *uploader.OSSConfig
}

// ProcessorStats 处理器统计
type ProcessorStats struct {
	TotalProcessed int64
	TotalFailed    int64
	ActiveTasks    int
	mutex          sync.RWMutex
}

// NewTaskProcessor 创建新的任务处理器
func NewTaskProcessor(cfg *config.Config) (*TaskProcessor, error) {
	conn, err := amqp091.Dial(cfg.Server.RabbitMQURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("failed to open a channel: %w", err)
	}

	// 设置QoS
	err = ch.Qos(cfg.Server.MaxConcurrent, 0, false)
	if err != nil {
		ch.Close()
		conn.Close()
		return nil, fmt.Errorf("failed to set QoS: %w", err)
	}

	// 创建子模块
	downloader := NewDownloader(&cfg.Server.Download)
	packager := packager.NewPackager(&cfg.Server.Packager)

	return &TaskProcessor{
		conn:        conn,
		ch:          ch,
		config:      cfg,
		downloader:  downloader,
		packager:    packager,
		projectData: make(map[string]*ProjectData),
		stats:       &ProcessorStats{},
	}, nil
}

// Start 启动任务处理器
func (tp *TaskProcessor) Start(ctx context.Context) error {
	// 声明交换机
	err := tp.ch.ExchangeDeclare(
		"tasks.direct",
		"direct",
		true,  // durable
		false, // auto-deleted
		false, // internal
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		return fmt.Errorf("declare exchange: %w", err)
	}

	// 启动后台任务
	go tp.packageCheckLoop(ctx)

	log.Println("Task processor started, waiting for messages...")

	// 保持运行
	<-ctx.Done()
	return nil
}

// ConsumeProject 消费指定项目的任务
func (tp *TaskProcessor) ConsumeProject(ctx context.Context, projectID string) error {
	queueName := fmt.Sprintf("tasks.%s", projectID)

	// 检查连接状态，如果连接关闭则重新连接
	if tp.ch.IsClosed() {
		log.Printf("RabbitMQ channel closed, reconnecting...")
		if err := tp.reconnect(); err != nil {
			return fmt.Errorf("reconnect: %w", err)
		}
	}

	log.Printf("[CONSUMER] Declaring queue: %s", queueName)

	// 声明队列
	q, err := tp.ch.QueueDeclare(
		queueName,
		true,  // durable
		false, // delete when unused
		false, // exclusive
		false, // no-wait
		map[string]interface{}{
			"x-message-ttl": 86400000, // 24小时过期
		},
	)
	if err != nil {
		log.Printf("[CONSUMER] Failed to declare queue %s, attempting to reconnect: %v", queueName, err)
		// 尝试重新连接
		if reconErr := tp.reconnect(); reconErr != nil {
			return fmt.Errorf("reconnect after queue declare failure: %w", reconErr)
		}
		// 重新尝试声明队列
		q, err = tp.ch.QueueDeclare(
			queueName,
			true,  // durable
			false, // delete when unused
			false, // exclusive
			false, // no-wait
			map[string]interface{}{
				"x-message-ttl": 86400000, // 24小时过期
			},
		)
		if err != nil {
			return fmt.Errorf("declare queue after reconnect: %w", err)
		}
	}
	log.Printf("[CONSUMER] Queue declared successfully: %s", q.Name)

	// 注意：不需要绑定到交换机，因为server直接发布到队列

	log.Printf("[CONSUMER] Starting to consume messages from queue: %s", q.Name)

	// 开始消费
	msgs, err := tp.ch.Consume(
		q.Name,
		"",    // consumer
		false, // auto-ack
		false, // exclusive
		false, // no-local
		false, // no-wait
		nil,   // args
	)
	if err != nil {
		log.Printf("[CONSUMER] Failed to register consumer for queue %s: %v", q.Name, err)
		return fmt.Errorf("register consumer: %w", err)
	}
	log.Printf("[CONSUMER] Consumer registered successfully for queue: %s", q.Name)

	// 处理消息
	go func() {
		log.Printf("[CONSUMER] Message processing goroutine started for project: %s", projectID)
		for d := range msgs {
			select {
			case <-ctx.Done():
				log.Printf("[CONSUMER] Context cancelled, stopping message processing for project: %s", projectID)
				return
			default:
				log.Printf("[CONSUMER] Received message for project %s, body length: %d bytes", projectID, len(d.Body))
				tp.processMessage(ctx, d)
			}
		}
		log.Printf("[CONSUMER] Message processing goroutine ended for project: %s", projectID)
	}()

	log.Printf("[CONSUMER] ConsumeProject setup completed for project: %s", projectID)
	return nil
}

// processMessage 处理单个消息
func (tp *TaskProcessor) processMessage(ctx context.Context, delivery amqp091.Delivery) {
	log.Printf("[CONSUMER] Processing message, delivery tag: %d", delivery.DeliveryTag)

	tp.stats.mutex.Lock()
	tp.stats.ActiveTasks++
	tp.stats.mutex.Unlock()

	defer func() {
		tp.stats.mutex.Lock()
		tp.stats.ActiveTasks--
		tp.stats.mutex.Unlock()
	}()

	// 解析任务消息
	var task TaskMessage
	log.Printf("[CONSUMER] Unmarshaling task message, body: %s", string(delivery.Body))
	if err := json.Unmarshal(delivery.Body, &task); err != nil {
		log.Printf("[CONSUMER] Failed to unmarshal task: %v", err)
		delivery.Nack(false, false) // 不重新入队
		tp.incrementFailed()
		return
	}

	log.Printf("[CONSUMER] Task parsed successfully: ID=%s, ProjectID=%s, BatchID=%s, URL=%s",
		task.ID, task.ProjectID, task.BatchID, task.URL)

	// 处理任务
	log.Printf("[CONSUMER] Starting task processing: %s", task.ID)
	success := tp.processTask(ctx, &task)

	if success {
		delivery.Ack(false)
		tp.incrementProcessed()
		log.Printf("[CONSUMER] Task completed successfully: %s", task.ID)
	} else {
		delivery.Nack(false, false) // 不重新入队，避免堵塞队列
		tp.incrementFailed()
		log.Printf("[CONSUMER] Task failed: %s", task.ID)
	}
}

// processTask 处理具体任务
func (tp *TaskProcessor) processTask(ctx context.Context, task *TaskMessage) bool {
	log.Printf("[TASK] Processing task %s: %s", task.ID, task.URL)

	// 创建项目工作目录
	projectDir := filepath.Join(tp.config.Basic.WorkDir, task.ProjectID, task.BatchID)
	log.Printf("[TASK] Project directory: %s", projectDir)

	// 下载文件
	log.Printf("[TASK] Starting download for task %s", task.ID)
	result := tp.downloader.DownloadFile(ctx, task.URL, projectDir)
	if !result.Success {
		log.Printf("[TASK] Download failed for task %s, URL %s: %s", task.ID, task.URL, result.Error)
		return false
	}
	log.Printf("[TASK] Download completed for task %s, local path: %s, size: %d bytes",
		task.ID, result.LocalPath, result.Size)

	// 记录到项目数据
	log.Printf("[TASK] Adding file to project data for task %s", task.ID)
	tp.addFileToProject(task.ProjectID, task.BatchID, result.LocalPath, result.Size, task.OSSConfig)

	// 检查是否需要打包
	log.Printf("[TASK] Checking if packaging is needed for task %s", task.ID)
	tp.checkAndPackage(ctx, task.ProjectID, task.BatchID)

	log.Printf("[TASK] Task processing completed successfully: %s", task.ID)
	return true
}

// addFileToProject 添加文件到项目数据
func (tp *TaskProcessor) addFileToProject(projectID, batchID, filePath string, size int64, ossConfig *uploader.OSSConfig) {
	tp.mutex.Lock()
	defer tp.mutex.Unlock()

	key := fmt.Sprintf("%s_%s", projectID, batchID)
	log.Printf("[PROJECT] Adding file to project data: %s, file: %s, size: %d bytes", key, filePath, size)

	if tp.projectData[key] == nil {
		tp.projectData[key] = &ProjectData{
			ProjectID: projectID,
			BatchID:   batchID,
			OSSConfig: ossConfig,
		}
		log.Printf("[PROJECT] Created new project data entry: %s", key)
	}

	tp.projectData[key].Files = append(tp.projectData[key].Files, filePath)
	tp.projectData[key].TotalSize += size

	log.Printf("[PROJECT] Project data updated: %s, total files: %d, total size: %d bytes",
		key, len(tp.projectData[key].Files), tp.projectData[key].TotalSize)
}

// checkAndPackage 检查并打包
func (tp *TaskProcessor) checkAndPackage(ctx context.Context, projectID, batchID string) {
	tp.mutex.RLock()
	key := fmt.Sprintf("%s_%s", projectID, batchID)
	data := tp.projectData[key]
	tp.mutex.RUnlock()

	log.Printf("[PACKAGE] Checking if packaging is needed for: %s", key)

	if data == nil {
		log.Printf("[PACKAGE] No project data found for: %s", key)
		return
	}

	// 检查大小是否达到阈值
	projectDir := filepath.Join(tp.config.Basic.WorkDir, projectID, batchID)
	log.Printf("[PACKAGE] Checking directory size: %s", projectDir)
	totalSize, shouldPackage, err := tp.packager.CheckDirectorySize(projectDir)
	if err != nil {
		log.Printf("[PACKAGE] Failed to check directory size for %s: %v", projectDir, err)
		return
	}

	log.Printf("[PACKAGE] Directory size check result: %s, size: %d bytes, should package: %v",
		key, totalSize, shouldPackage)

	if shouldPackage {
		log.Printf("[PACKAGE] Starting package and upload for: %s", key)
		tp.packageAndUpload(ctx, data)
	} else {
		log.Printf("[PACKAGE] Package threshold not reached for: %s", key)
	}
}

// packageAndUpload 打包并上传
func (tp *TaskProcessor) packageAndUpload(ctx context.Context, data *ProjectData) {
	log.Printf("[UPLOAD] Starting package and upload for project %s, batch %s", data.ProjectID, data.BatchID)

	// 创建上传器
	if tp.uploader == nil && data.OSSConfig != nil {
		log.Printf("[UPLOAD] Creating uploader with OSS config")
		tp.uploader = uploader.NewUploader(&tp.config.Server.OSS, data.OSSConfig)
		log.Printf("[UPLOAD] Uploader created successfully")
	}

	if tp.uploader == nil {
		log.Printf("[UPLOAD] No uploader configured for project %s", data.ProjectID)
		return
	}

	// 创建包名
	packageName := tp.packager.CreatePackageName(data.ProjectID, data.BatchID)
	log.Printf("[UPLOAD] Generated package name: %s", packageName)

	// 打包
	sourceDir := filepath.Join(tp.config.Basic.WorkDir, data.ProjectID, data.BatchID)
	outputDir := filepath.Join(tp.config.Basic.TempDir, "packages")
	log.Printf("[UPLOAD] Packaging files from %s to %s", sourceDir, outputDir)

	result := tp.packager.PackageFiles(sourceDir, outputDir, packageName)
	if !result.Success {
		log.Printf("[UPLOAD] Package failed: %s", result.Error)
		return
	}
	log.Printf("[UPLOAD] Package created successfully: %s, files: %d, size: %d bytes",
		result.PackagePath, result.FileCount, result.TotalSize)

	// 上传到OSS
	ossKey := tp.uploader.GenerateOSSKey(data.ProjectID, data.BatchID, packageName+".tar.gz")
	log.Printf("[UPLOAD] Starting upload to OSS: %s", ossKey)
	uploadResult := tp.uploader.UploadFile(ctx, result.PackagePath, ossKey)
	if !uploadResult.Success {
		log.Printf("[UPLOAD] Upload failed: %s", uploadResult.Error)
		return
	}
	log.Printf("[UPLOAD] Upload completed successfully: %s, size: %d bytes, speed: %d bytes/s",
		ossKey, uploadResult.Size, uploadResult.UploadSpeed)

	// 清理本地文件
	log.Printf("[UPLOAD] Cleaning up local files")
	err := tp.packager.CleanupFiles(sourceDir)
	if err != nil {
		log.Printf("[UPLOAD] Failed to cleanup source directory %s: %v", sourceDir, err)
	} else {
		log.Printf("[UPLOAD] Source directory cleaned up: %s", sourceDir)
	}

	err = tp.uploader.DeleteLocalFile(result.PackagePath)
	if err != nil {
		log.Printf("[UPLOAD] Failed to delete package file %s: %v", result.PackagePath, err)
	} else {
		log.Printf("[UPLOAD] Package file deleted: %s", result.PackagePath)
	}

	// 清理项目数据
	tp.mutex.Lock()
	key := fmt.Sprintf("%s_%s", data.ProjectID, data.BatchID)
	delete(tp.projectData, key)
	tp.mutex.Unlock()
	log.Printf("[UPLOAD] Project data cleaned up: %s", key)

	log.Printf("[UPLOAD] Package and upload process completed successfully for project %s, batch %s",
		data.ProjectID, data.BatchID)
}

// packageCheckLoop 定期检查打包
func (tp *TaskProcessor) packageCheckLoop(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			tp.checkAllProjects(ctx)
		}
	}
}

// checkAllProjects 检查所有项目
func (tp *TaskProcessor) checkAllProjects(ctx context.Context) {
	tp.mutex.RLock()
	projects := make([]*ProjectData, 0, len(tp.projectData))
	for _, data := range tp.projectData {
		projects = append(projects, data)
	}
	tp.mutex.RUnlock()

	for _, data := range projects {
		// 检查文件修改时间，如果超过5分钟没有新文件，就强制打包
		projectDir := filepath.Join(tp.config.Basic.WorkDir, data.ProjectID, data.BatchID)
		if tp.shouldForcePackage(projectDir) {
			tp.packageAndUpload(ctx, data)
		}
	}
}

// shouldForcePackage 是否应该强制打包
func (tp *TaskProcessor) shouldForcePackage(dir string) bool {
	info, err := os.Stat(dir)
	if err != nil {
		return false
	}

	// 如果目录超过5分钟没有修改，强制打包
	return time.Since(info.ModTime()) > 5*time.Minute
}

// GetStats 获取统计信息
func (tp *TaskProcessor) GetStats() map[string]interface{} {
	tp.stats.mutex.RLock()
	defer tp.stats.mutex.RUnlock()

	return map[string]interface{}{
		"total_processed": tp.stats.TotalProcessed,
		"total_failed":    tp.stats.TotalFailed,
		"active_tasks":    tp.stats.ActiveTasks,
	}
}

func (tp *TaskProcessor) incrementProcessed() {
	tp.stats.mutex.Lock()
	tp.stats.TotalProcessed++
	tp.stats.mutex.Unlock()
}

func (tp *TaskProcessor) incrementFailed() {
	tp.stats.mutex.Lock()
	tp.stats.TotalFailed++
	tp.stats.mutex.Unlock()
}

// reconnect 重新连接RabbitMQ
func (tp *TaskProcessor) reconnect() error {
	// 关闭旧连接
	if tp.ch != nil {
		tp.ch.Close()
	}
	if tp.conn != nil {
		tp.conn.Close()
	}

	// 建立新连接
	conn, err := amqp091.Dial(tp.config.Server.RabbitMQURL)
	if err != nil {
		return fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return fmt.Errorf("failed to open a channel: %w", err)
	}

	// 设置QoS
	err = ch.Qos(tp.config.Server.MaxConcurrent, 0, false)
	if err != nil {
		ch.Close()
		conn.Close()
		return fmt.Errorf("failed to set QoS: %w", err)
	}

	tp.conn = conn
	tp.ch = ch

	log.Printf("RabbitMQ reconnected successfully")
	return nil
}

// Close 关闭任务处理器
func (tp *TaskProcessor) Close() {
	if tp.ch != nil {
		tp.ch.Close()
	}
	if tp.conn != nil {
		tp.conn.Close()
	}
}
