package downloader

import (
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"download-scheduler/worker/internal/config"
)

// Downloader 文件下载器
type Downloader struct {
	config     *config.DownloadConfig
	httpClient *http.Client
}

// NewDownloader 创建新的下载器
func NewDownloader(cfg *config.DownloadConfig) *Downloader {
	return &Downloader{
		config: cfg,
		httpClient: &http.Client{
			Timeout: cfg.Timeout,
		},
	}
}

// DownloadResult 下载结果
type DownloadResult struct {
	URL       string        `json:"url"`
	LocalPath string        `json:"localPath"`
	Size      int64         `json:"size"`
	Checksum  string        `json:"checksum"`
	Duration  time.Duration `json:"duration"`
	Success   bool          `json:"success"`
	Error     string        `json:"error,omitempty"`
}

// DownloadFile 下载单个文件
func (d *Downloader) DownloadFile(ctx context.Context, url, destDir string) *DownloadResult {
	start := time.Now()
	result := &DownloadResult{
		URL: url,
	}

	log.Printf("[DOWNLOADER] Starting download: %s", url)

	// 创建目标目录
	if err := os.MkdirAll(destDir, 0755); err != nil {
		result.Error = fmt.Sprintf("create directory: %v", err)
		log.Printf("[DOWNLOADER] Failed to create directory %s: %v", destDir, err)
		return result
	}
	log.Printf("[DOWNLOADER] Created destination directory: %s", destDir)

	// 获取文件名
	filename := filepath.Base(url)
	if filename == "." || filename == "/" {
		filename = fmt.Sprintf("file_%d", time.Now().Unix())
	}

	localPath := filepath.Join(destDir, filename)
	result.LocalPath = localPath
	log.Printf("[DOWNLOADER] Target file path: %s", localPath)

	// 重试机制
	var lastErr error
	for attempt := 0; attempt < d.config.Retry; attempt++ {
		if attempt > 0 {
			log.Printf("[DOWNLOADER] Retry attempt %d/%d for %s", attempt+1, d.config.Retry, url)
			select {
			case <-ctx.Done():
				result.Error = "context cancelled"
				log.Printf("[DOWNLOADER] Download cancelled by context: %s", url)
				return result
			case <-time.After(time.Duration(attempt) * time.Second):
				// 指数退避
			}
		}

		log.Printf("[DOWNLOADER] Attempting download (attempt %d): %s", attempt+1, url)
		err := d.downloadWithRetry(ctx, url, localPath)
		if err == nil {
			result.Success = true
			log.Printf("[DOWNLOADER] Download successful: %s", url)
			break
		}
		lastErr = err
		log.Printf("[DOWNLOADER] Download attempt %d failed: %v", attempt+1, err)
	}

	if !result.Success {
		result.Error = fmt.Sprintf("download failed after %d retries: %v", d.config.Retry, lastErr)
		log.Printf("[DOWNLOADER] Download failed permanently: %s, error: %s", url, result.Error)
		return result
	}

	// 计算文件信息
	fileInfo, err := os.Stat(localPath)
	if err != nil {
		result.Error = fmt.Sprintf("stat file: %v", err)
		log.Printf("[DOWNLOADER] Failed to stat downloaded file %s: %v", localPath, err)
		return result
	}
	result.Size = fileInfo.Size()
	log.Printf("[DOWNLOADER] Downloaded file size: %d bytes", result.Size)

	// 计算校验和
	checksum, err := d.calculateChecksum(localPath)
	if err != nil {
		result.Error = fmt.Sprintf("calculate checksum: %v", err)
		log.Printf("[DOWNLOADER] Failed to calculate checksum for %s: %v", localPath, err)
		return result
	}
	result.Checksum = checksum

	result.Duration = time.Since(start)
	log.Printf("[DOWNLOADER] Download completed successfully: %s, size: %d bytes, duration: %v, checksum: %s",
		url, result.Size, result.Duration, result.Checksum)
	return result
}

// downloadWithRetry 执行实际下载
func (d *Downloader) downloadWithRetry(ctx context.Context, url, localPath string) error {
	log.Printf("[DOWNLOADER] Creating HTTP request for: %s", url)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		log.Printf("[DOWNLOADER] Failed to create HTTP request: %v", err)
		return fmt.Errorf("create request: %w", err)
	}

	// 设置真实的HTTP headers，模拟真实浏览器请求
	req.Header.Set("User-Agent", d.config.UserAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "none")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Connection", "keep-alive")
	log.Printf("[DOWNLOADER] HTTP headers set, User-Agent: %s", d.config.UserAgent)

	log.Printf("[DOWNLOADER] Sending HTTP request to: %s", url)
	resp, err := d.httpClient.Do(req)
	if err != nil {
		log.Printf("[DOWNLOADER] HTTP request failed: %v", err)
		return fmt.Errorf("http request: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("[DOWNLOADER] HTTP response received, status: %d, content-length: %d", resp.StatusCode, resp.ContentLength)
	if resp.StatusCode != http.StatusOK {
		log.Printf("[DOWNLOADER] HTTP status error: %d", resp.StatusCode)
		return fmt.Errorf("http status: %d", resp.StatusCode)
	}

	// 检查文件大小
	if resp.ContentLength > d.config.MaxFileSize {
		log.Printf("[DOWNLOADER] File too large: %d bytes (max: %d)", resp.ContentLength, d.config.MaxFileSize)
		return fmt.Errorf("file too large: %d bytes", resp.ContentLength)
	}

	// 创建临时文件
	tmpPath := localPath + ".tmp"
	log.Printf("[DOWNLOADER] Creating temporary file: %s", tmpPath)
	file, err := os.Create(tmpPath)
	if err != nil {
		log.Printf("[DOWNLOADER] Failed to create temporary file: %v", err)
		return fmt.Errorf("create file: %w", err)
	}
	defer file.Close()

	// 下载文件
	log.Printf("[DOWNLOADER] Starting file download, buffer size: %d bytes", d.config.BufferSize)
	buffer := make([]byte, d.config.BufferSize)
	bytesWritten, err := io.CopyBuffer(file, resp.Body, buffer)
	if err != nil {
		log.Printf("[DOWNLOADER] Failed to copy data: %v", err)
		os.Remove(tmpPath)
		return fmt.Errorf("copy data: %w", err)
	}
	log.Printf("[DOWNLOADER] Downloaded %d bytes to temporary file", bytesWritten)

	// 原子性移动文件
	log.Printf("[DOWNLOADER] Moving temporary file to final location: %s", localPath)
	err = os.Rename(tmpPath, localPath)
	if err != nil {
		log.Printf("[DOWNLOADER] Failed to rename file: %v", err)
		os.Remove(tmpPath)
		return fmt.Errorf("rename file: %w", err)
	}

	log.Printf("[DOWNLOADER] File download completed successfully: %s", localPath)
	return nil
}

// calculateChecksum 计算文件校验和
func (d *Downloader) calculateChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	_, err = io.Copy(hash, file)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// DownloadBatch 批量下载文件
func (d *Downloader) DownloadBatch(ctx context.Context, urls []string, destDir string) []*DownloadResult {
	results := make([]*DownloadResult, 0, len(urls))
	sem := make(chan struct{}, d.config.Concurrent)

	for _, url := range urls {
		select {
		case <-ctx.Done():
			// 上下文取消，返回已完成的结果
			return results
		case sem <- struct{}{}:
			go func(u string) {
				defer func() { <-sem }()
				result := d.DownloadFile(ctx, u, destDir)
				results = append(results, result)
			}(url)
		}
	}

	// 等待所有下载完成
	for i := 0; i < cap(sem); i++ {
		sem <- struct{}{}
	}

	return results
}

// GetDownloadProgress 获取下载进度（简化版，实际可以更复杂）
func (d *Downloader) GetDownloadProgress() map[string]interface{} {
	return map[string]interface{}{
		"active_downloads": 0, // 可以通过goroutine计数实现
		"total_downloaded": 0,
		"total_failed":     0,
	}
}
