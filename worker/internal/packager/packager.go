package packager

import (
	"archive/tar"
	"compress/gzip"
	"crypto/md5"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"download-scheduler/worker/internal/config"
)

// Packager 文件打包器
type Packager struct {
	config *config.PackagerConfig
}

// NewPackager 创建新的打包器
func NewPackager(cfg *config.PackagerConfig) *Packager {
	return &Packager{
		config: cfg,
	}
}

// PackageResult 打包结果
type PackageResult struct {
	PackagePath string        `json:"packagePath"`
	FileCount   int           `json:"fileCount"`
	TotalSize   int64         `json:"totalSize"`
	Checksum    string        `json:"checksum"`
	Duration    time.Duration `json:"duration"`
	Success     bool          `json:"success"`
	Error       string        `json:"error,omitempty"`
}

// PackageFiles 打包指定目录中的文件
func (p *Packager) PackageFiles(sourceDir, outputDir, packageName string) *PackageResult {
	start := time.Now()
	result := &PackageResult{}

	log.Printf("[PACKAGER] Starting package creation: %s", packageName)
	log.Printf("[PACKAGER] Source directory: %s", sourceDir)
	log.Printf("[PACKAGER] Output directory: %s", outputDir)

	// 创建输出目录
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		result.Error = fmt.Sprintf("create output directory: %v", err)
		log.Printf("[PACKAGER] Failed to create output directory %s: %v", outputDir, err)
		return result
	}
	log.Printf("[PACKAGER] Output directory created successfully")

	// 生成包文件路径
	packagePath := filepath.Join(outputDir, packageName+".tar.gz")
	result.PackagePath = packagePath
	log.Printf("[PACKAGER] Package file path: %s", packagePath)

	// 创建包文件
	packageFile, err := os.Create(packagePath)
	if err != nil {
		result.Error = fmt.Sprintf("create package file: %v", err)
		log.Printf("[PACKAGER] Failed to create package file %s: %v", packagePath, err)
		return result
	}
	defer packageFile.Close()
	log.Printf("[PACKAGER] Package file created successfully")

	// 创建gzip writer
	gzipWriter := gzip.NewWriter(packageFile)
	defer gzipWriter.Close()
	log.Printf("[PACKAGER] Gzip writer created")

	// 创建tar writer
	tarWriter := tar.NewWriter(gzipWriter)
	defer tarWriter.Close()
	log.Printf("[PACKAGER] Tar writer created")

	// 遍历源目录并添加文件到tar
	log.Printf("[PACKAGER] Starting to add files from source directory")
	err = p.addFilesToTar(tarWriter, sourceDir, "", result)
	if err != nil {
		result.Error = fmt.Sprintf("add files to tar: %v", err)
		log.Printf("[PACKAGER] Failed to add files to tar: %v", err)
		return result
	}
	log.Printf("[PACKAGER] Added %d files to package", result.FileCount)

	// 关闭writers以确保数据写入
	log.Printf("[PACKAGER] Closing writers to finalize package")
	tarWriter.Close()
	gzipWriter.Close()
	packageFile.Close()

	// 计算包文件信息
	packageInfo, err := os.Stat(packagePath)
	if err != nil {
		result.Error = fmt.Sprintf("stat package file: %v", err)
		log.Printf("[PACKAGER] Failed to stat package file %s: %v", packagePath, err)
		return result
	}
	result.TotalSize = packageInfo.Size()
	log.Printf("[PACKAGER] Package file size: %d bytes", result.TotalSize)

	// 计算校验和
	log.Printf("[PACKAGER] Calculating package checksum")
	checksum, err := p.calculateChecksum(packagePath)
	if err != nil {
		result.Error = fmt.Sprintf("calculate checksum: %v", err)
		log.Printf("[PACKAGER] Failed to calculate checksum: %v", err)
		return result
	}
	result.Checksum = checksum

	result.Duration = time.Since(start)
	result.Success = true

	log.Printf("[PACKAGER] Package created successfully: %s, files: %d, size: %d bytes, duration: %v, checksum: %s",
		packagePath, result.FileCount, result.TotalSize, result.Duration, result.Checksum)
	return result
}

// addFilesToTar 递归添加文件到tar
func (p *Packager) addFilesToTar(tarWriter *tar.Writer, sourceDir, basePath string, result *PackageResult) error {
	return filepath.Walk(sourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录本身
		if info.IsDir() {
			return nil
		}

		// 构建tar中的文件路径
		relPath, err := filepath.Rel(sourceDir, path)
		if err != nil {
			return err
		}

		if basePath != "" {
			relPath = filepath.Join(basePath, relPath)
		}

		// 使用Unix路径分隔符
		relPath = strings.ReplaceAll(relPath, "\\", "/")

		// 创建tar header
		header, err := tar.FileInfoHeader(info, "")
		if err != nil {
			return err
		}
		header.Name = relPath

		// 写入header
		if err := tarWriter.WriteHeader(header); err != nil {
			return err
		}

		// 写入文件内容
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = io.Copy(tarWriter, file)
		if err != nil {
			return err
		}

		result.FileCount++
		return nil
	})
}

// calculateChecksum 计算文件校验和
func (p *Packager) calculateChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	_, err = io.Copy(hash, file)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// CheckDirectorySize 检查目录大小是否达到打包阈值
func (p *Packager) CheckDirectorySize(dir string) (int64, bool, error) {
	var totalSize int64
	var fileCount int

	log.Printf("[PACKAGER] Checking directory size: %s", dir)

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			log.Printf("[PACKAGER] Error walking path %s: %v", path, err)
			return err
		}
		if !info.IsDir() {
			totalSize += info.Size()
			fileCount++
		}
		return nil
	})

	if err != nil {
		log.Printf("[PACKAGER] Failed to walk directory %s: %v", dir, err)
		return 0, false, err
	}

	// 转换GB到字节
	thresholdBytes := int64(p.config.MaxSizeGB) * 1024 * 1024 * 1024
	shouldPackage := totalSize >= thresholdBytes

	log.Printf("[PACKAGER] Directory size check completed: %s, files: %d, total size: %d bytes, threshold: %d bytes, should package: %v",
		dir, fileCount, totalSize, thresholdBytes, shouldPackage)

	return totalSize, shouldPackage, nil
}

// CleanupFiles 清理已打包的文件
func (p *Packager) CleanupFiles(dir string) error {
	return os.RemoveAll(dir)
}

// CreatePackageName 创建包文件名
func (p *Packager) CreatePackageName(projectID, batchID string) string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("%s_%s_%s", projectID, batchID, timestamp)
}

// ValidatePackage 验证包文件
func (p *Packager) ValidatePackage(packagePath string) error {
	// 打开文件
	file, err := os.Open(packagePath)
	if err != nil {
		return fmt.Errorf("open package: %w", err)
	}
	defer file.Close()

	// 创建gzip reader
	gzipReader, err := gzip.NewReader(file)
	if err != nil {
		return fmt.Errorf("create gzip reader: %w", err)
	}
	defer gzipReader.Close()

	// 创建tar reader
	tarReader := tar.NewReader(gzipReader)

	// 遍历tar文件以验证结构
	fileCount := 0
	for {
		header, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("read tar header: %w", err)
		}

		// 验证文件名
		if header.Name == "" {
			return fmt.Errorf("empty file name in tar")
		}

		fileCount++
	}

	if fileCount == 0 {
		return fmt.Errorf("empty package")
	}

	return nil
}

// GetPackageInfo 获取包文件信息
func (p *Packager) GetPackageInfo(packagePath string) (map[string]interface{}, error) {
	info, err := os.Stat(packagePath)
	if err != nil {
		return nil, err
	}

	checksum, err := p.calculateChecksum(packagePath)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"path":       packagePath,
		"size":       info.Size(),
		"checksum":   checksum,
		"created_at": info.ModTime(),
	}, nil
}
