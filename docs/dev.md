# 开发环境指南

本文档介绍如何在本地搭建和运行 Download Scheduler 项目的开发环境。

## 系统要求

- **Go**: 1.23+
- **Node.js**: 20+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd download-scheduler
```

### 2. 准备中间件

项目依赖三个核心中间件：MongoDB、RabbitMQ、Redis。

#### 方式一：使用 Docker Compose（推荐）

```bash
# 启动所有中间件
docker-compose up -d mongodb redis rabbitmq

# 或者使用脚本
./scripts/setup-middleware.sh
```

#### 方式二：本地安装

如果你更喜欢本地安装，请确保以下服务运行在默认端口：

- MongoDB: `localhost:27017`
- Redis: `localhost:6379`
- RabbitMQ: `localhost:5672` (管理界面: `localhost:15672`)

### 3. 配置环境

#### Server 配置

复制并修改配置文件：

```bash
cd server
cp configs/config-test.yaml configs/config.yaml
```

主要配置项：

```yaml
database:
  mongodb:
    uri: *******************************************
    database: download_scheduler_dev
  redis:
    addr: localhost:6379
    password: "password123"

mq:
  rabbitmq:
    url: amqp://admin:password123@localhost:5672/
```

#### Worker 配置

```bash
cd worker
cp configs/config-test.yaml configs/config.yaml
```

#### Web 配置

```bash
cd web
cp env.example .env.local
```

修改 `.env.local` 中的 API 地址：

```env
NEXT_PUBLIC_API_URL=http://localhost:8080
```

### 4. 安装依赖

#### Server 依赖

```bash
cd server
go mod download
go mod tidy
```

#### Worker 依赖

```bash
cd worker
go mod download
go mod tidy
```

#### Web 依赖

```bash
cd web
npm install
```

### 5. 启动服务

按以下顺序启动各个服务：

#### 启动 Server

```bash
cd server
make dev
```

Server 将在 `http://localhost:8080` 启动，你可以访问 `http://localhost:8080/ping` 检查服务状态。

#### 启动 Worker

```bash
cd worker
make dev
```

Worker 会自动连接到 Server 并注册自己。

#### 启动 Web

```bash
cd web
npm run dev
```

Web 界面将在 `http://localhost:3000` 启动。

## 开发工具和命令

### Server 开发

```bash
cd server

# 开发模式运行
make dev

# 运行测试
make test

# 代码格式化
make fmt

# 代码检查
make vet

# 构建二进制文件
make build

# 查看所有可用命令
make help
```

### Worker 开发

```bash
cd worker

# 开发模式运行
make dev

# 运行测试
make test

# 代码格式化
make fmt

# 代码检查
make vet

# 构建二进制文件
make build
```

### Web 开发

```bash
cd web

# 开发模式运行
npm run dev

# 运行测试
npm test

# 运行测试（监听模式）
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage

# 构建生产版本
npm run build

# 启动生产版本
npm start

# 代码检查
npm run lint
```

## 中间件管理

### Docker Compose 命令

```bash
# 启动所有中间件
docker-compose up -d

# 启动特定服务
docker-compose up -d mongodb
docker-compose up -d redis
docker-compose up -d rabbitmq

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs mongodb
docker-compose logs redis
docker-compose logs rabbitmq

# 停止服务
docker-compose down

# 停止并删除数据卷（谨慎使用）
docker-compose down -v
```

### 中间件访问信息

| 服务     | 地址            | 用户名 | 密码        | 管理界面               |
| -------- | --------------- | ------ | ----------- | ---------------------- |
| MongoDB  | localhost:27017 | admin  | password123 | -                      |
| Redis    | localhost:6379  | -      | password123 | -                      |
| RabbitMQ | localhost:5672  | admin  | password123 | http://localhost:15672 |

## 环境变量说明

### Server 环境变量

| 变量名         | 说明                | 默认值                |
| -------------- | ------------------- | --------------------- |
| `CONFIG_PATH`  | 配置文件路径        | `configs/config.yaml` |
| `GIN_MODE`     | Gin 运行模式        | `debug`               |
| `MONGO_URI`    | MongoDB 连接字符串  | 从配置文件读取        |
| `REDIS_ADDR`   | Redis 地址          | 从配置文件读取        |
| `RABBITMQ_URL` | RabbitMQ 连接字符串 | 从配置文件读取        |

### Worker 环境变量

| 变量名        | 说明         | 默认值                         |
| ------------- | ------------ | ------------------------------ |
| `CONFIG_PATH` | 配置文件路径 | `configs/config.yaml`          |
| `SERVER_URL`  | Server 地址  | `http://localhost:8080`        |
| `WORK_DIR`    | 工作目录     | `/tmp/download-scheduler/work` |
| `TEMP_DIR`    | 临时目录     | `/tmp/download-scheduler/temp` |

### Web 环境变量

| 变量名                | 说明         | 默认值                  |
| --------------------- | ------------ | ----------------------- |
| `NEXT_PUBLIC_API_URL` | API 服务地址 | `http://localhost:8080` |
| `NODE_ENV`            | Node.js 环境 | `development`           |

## 常见问题

### 1. 中间件连接失败

**问题**: Server 启动时报错 "Failed to initialize database"

**解决方案**:

1. 确认 Docker Compose 服务已启动：`docker-compose ps`
2. 检查配置文件中的连接信息是否正确
3. 查看中间件日志：`docker-compose logs <service-name>`

### 2. Worker 无法连接 Server

**问题**: Worker 启动时报错 "Failed to connect to server"

**解决方案**:

1. 确认 Server 已启动并运行在 8080 端口
2. 检查 Worker 配置文件中的 `server_url` 设置
3. 确认防火墙没有阻止连接

### 3. Web 界面 API 调用失败

**问题**: 前端页面显示 API 错误

**解决方案**:

1. 确认 Server 服务正常运行
2. 检查 `.env.local` 中的 `NEXT_PUBLIC_API_URL` 设置
3. 查看浏览器开发者工具的网络请求

### 4. 端口冲突

**问题**: 服务启动时报错端口已被占用

**解决方案**:

1. 检查端口占用：`lsof -i :8080` (macOS/Linux) 或 `netstat -ano | findstr :8080` (Windows)
2. 停止占用端口的进程或修改配置文件中的端口设置

## 开发最佳实践

### 1. 代码提交前检查

```bash
# Server
cd server && make fmt && make vet && make test

# Worker
cd worker && make fmt && make vet && make test

# Web
cd web && npm run lint && npm test
```

### 2. 数据库迁移

开发过程中如果需要重置数据库：

```bash
# 停止服务
docker-compose stop mongodb

# 删除数据卷
docker-compose rm -v mongodb

# 重新启动
docker-compose up -d mongodb
```

### 3. 日志查看

各服务的日志文件位置：

- Server: `server/logs/server.log`
- Worker: `worker/logs/worker.log`
- Web: 控制台输出

### 4. 性能监控

开发环境下可以通过以下方式监控性能：

- RabbitMQ 管理界面: http://localhost:15672
- Server API 健康检查: http://localhost:8080/ping
- 系统资源监控: `docker stats`

## 故障排除

### 重置开发环境

如果遇到无法解决的问题，可以完全重置开发环境：

```bash
# 停止所有服务
docker-compose down -v

# 清理 Go 模块缓存
go clean -modcache

# 清理 Node.js 依赖
cd web && rm -rf node_modules package-lock.json && npm install

# 重新启动
./scripts/setup-middleware.sh
```

### 获取帮助

- 查看项目文档: `docs/` 目录
- 查看 API 文档: `docs/api-documentation.md`
- 查看产品需求: `docs/prd.md`
