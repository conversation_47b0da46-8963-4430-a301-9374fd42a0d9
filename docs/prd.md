# 分布式下载调度系统 - 需求与技术方案

## 一、项目概述

### 1.1 项目背景
需要构建一个分布式下载调度系统，支持千万至亿级别的下载任务管理。系统通过多个Worker节点并行下载文件，自动打包压缩后上传到对象存储（OSS）。

### 1.2 核心需求
- **海量任务支持**：支持千万到亿级别的下载任务
- **分布式架构**：多Worker节点并行处理
- **自动打包上传**：下载文件自动打包并上传OSS
- **实时监控**：Web界面实时展示任务进度和Worker状态
- **任务控制**：支持暂停、继续等操作
- **多云存储**：支持阿里云OSS、AWS S3、MinIO等

### 1.3 技术栈
- **后端**：Golang
- **前端**：Next.js + React
- **数据库**：MongoDB（主存储）+ Redis（缓存）
- **消息队列**：RabbitMQ
- **对象存储**：阿里云OSS/AWS S3/MinIO

## 二、系统架构

### 2.1 架构图

```mermaid
graph TB
    subgraph "用户端"
        UI[Web管理界面<br/>Next.js]
        API[API客户端]
    end
    
    subgraph "服务端"
        Server[调度服务器<br/>Golang]
        MongoDB[(MongoDB<br/>项目/批次/包信息)]
        Redis[(Redis<br/>实时统计/缓存)]
        MQ[RabbitMQ<br/>任务队列]
    end
    
    subgraph "执行层"
        W1[Worker 1]
        W2[Worker 2]
        Wn[Worker N]
    end
    
    subgraph "存储层"
        OSS[对象存储<br/>OSS/S3/MinIO]
    end
    
    UI --> Server
    API --> Server
    Server --> MongoDB
    Server --> Redis
    Server --> MQ
    MQ --> W1
    MQ --> W2
    MQ --> Wn
    W1 --> OSS
    W2 --> OSS
    Wn --> OSS
    Server <-.WebSocket.-> W1
    Server <-.WebSocket.-> W2
    Server <-.WebSocket.-> Wn
```

### 2.2 WebSocket 实时架构

系统采用 WebSocket 实时连接架构，替代传统的 HTTP 轮询机制：

#### 2.2.1 连接管理

- **Worker 连接**：每个 Worker 通过 WebSocket 建立长连接
- **连接池**：Server 维护活跃连接映射表
- **自动重连**：Worker 断线自动重连，保证高可用性
- **状态实时**：连接状态即 Worker 状态，无延迟

#### 2.2.2 消息通信

```
Worker -> Server:
- activate: Worker 激活请求
- stats: 实时统计信息
- ping: 心跳检测

Server -> Worker:  
- activate_ack: 激活确认
- config: 配置更新
- control: 控制命令
- pong: 心跳响应
```

#### 2.2.3 优势特性

1. **真正实时**：状态变更立即感知（毫秒级）
2. **高效通信**：减少HTTP开销，单连接复用
3. **双向推送**：支持Server主动推送消息
4. **故障快速恢复**：连接断开立即检测

### 2.3 核心组件

1. **Scheduler Server（调度服务器）**
   - API服务（项目管理、任务提交、监控数据）
   - Worker管理（注册、WebSocket连接管理、实时状态）
   - 任务调度（通过RabbitMQ分发）
   - 实时监控（WebSocket推送）
   - 实时消息推送（配置更新、控制命令）

2. **Worker（工作节点）**
   - WebSocket连接到Server（实时通信）
   - 从RabbitMQ消费任务
   - 执行文件下载
   - 文件打包压缩
   - 上传到OSS
   - 实时状态上报（通过WebSocket）
   - 接收实时配置更新和控制命令

3. **Web UI（管理界面）**
   - 项目管理
   - 实时监控面板
   - Worker状态展示
   - 任务控制（暂停/继续）

## 三、数据模型设计

### 3.1 MongoDB数据结构

```javascript
// 1. projects 集合 - 项目配置
{
  "_id": "project_xxxxx",
  "name": "项目名称",
  "token": "Bearer token_xxxxx",  // API认证token
  "config": {
    "packSizeGB": 10,              // 打包大小（GB）
    "ossConfig": {
      "provider": "aliyun",        // aliyun|aws|minio
      "endpoint": "oss-cn-beijing.aliyuncs.com",
      "bucket": "download-data",
      "accessKey": "encrypted_access_key",
      "secretKey": "encrypted_secret_key",
      "prefix": "project1/",       // OSS路径前缀
      "region": "cn-beijing",      // 地域（AWS S3需要）
      "forcePathStyle": false      // 强制使用路径样式（MinIO需要）
    },
    "concurrent": 5,               // 单Worker并发数
    "retryTimes": 3,              // 失败重试次数
    "downloadTimeout": 300         // 下载超时（秒）
  },
  "status": "active",             // active|paused|completed
  "createdAt": ISODate("2024-01-15T10:00:00Z"),
  "updatedAt": ISODate("2024-01-15T10:00:00Z")
}

// 2. workers 集合 - Worker信息
{
  "_id": "worker_xxxxx",
  "name": "worker-01",
  "host": "************",
  "port": 8081,
  "status": "active",              // active|inactive
  "version": "1.0.0",
  "capabilities": {
    "maxConcurrent": 10,
    "diskSpace": 500,              // GB
    "bandwidth": 1000              // Mbps
  },
  "stats": {
    "cpuUsage": 45.5,             // 百分比
    "memoryUsage": 60.2,          // 百分比
    "diskUsage": 30.5,            // 百分比
    "activeTasks": 3,
    "totalProcessed": 150000,
    "totalFailed": 500
  },
  "projects": ["project_xxxxx"],   // 处理的项目列表
  "lastHeartbeat": ISODate("2024-01-15T10:30:00Z"),
  "createdAt": ISODate("2024-01-15T10:00:00Z")
}

// 3. batches 集合 - 任务批次
{
  "_id": "batch_xxxxx",
  "projectId": "project_xxxxx",
  "batchNo": 1,
  "totalCount": 1000000,
  "status": "processing",          // pending|processing|completed
  "stats": {
    "pending": 800000,
    "downloading": 50000,
    "completed": 140000,
    "failed": 10000
  },
  "createdAt": ISODate("2024-01-15T10:00:00Z"),
  "updatedAt": ISODate("2024-01-15T10:30:00Z"),
  "completedAt": null
}

// 4. packages 集合 - 数据包记录
{
  "_id": "package_xxxxx",
  "projectId": "project_xxxxx",
  "batchId": "batch_xxxxx",
  "workerId": "worker_xxxxx",
  "ossPath": "project1/2024-01-15/package_xxxxx.tar.gz",
  "size": 10737418240,            // 字节
  "fileCount": 5000,
  "checksum": "md5_hash_value",
  "uploadSpeed": 104857600,        // 字节/秒
  "createdAt": ISODate("2024-01-15T10:30:00Z"),
  "uploadedAt": ISODate("2024-01-15T10:35:00Z")
}
```

### 3.2 Redis数据结构

```redis
# 1. 项目实时统计
HSET project:stats:{project_id}
  total_tasks 1000000
  pending_tasks 800000
  downloading_tasks 50000
  completed_tasks 140000
  failed_tasks 10000
  total_size 1099511627776        # 总下载字节数
  download_speed 104857600         # 当前下载速度(字节/秒)
  success_rate 93.33              # 成功率百分比
  last_update 1705312800          # 时间戳

# 2. Worker实时状态
HSET worker:stats:{worker_id}
  cpu_usage 45.5
  memory_usage 60.2
  disk_usage 30.5
  active_tasks 3
  download_speed 52428800
  upload_speed 20971520
  last_heartbeat 1705312800

# 3. 项目控制状态
SET project:control:{project_id} "active"  # active|paused

# 4. 队列深度监控
SET queue:depth:{project_id} 850000

# 5. 速度历史（用于图表）
ZADD project:speed:{project_id} 
  1705312800 104857600
  1705312805 102400000
  ...
```

### 3.3 RabbitMQ队列设计

```yaml
exchanges:
  - name: tasks.direct
    type: direct
    durable: true

queues:
  # 任务队列（按项目划分）
  - name: tasks.{project_id}
    durable: true
    arguments:
      x-max-priority: 10          # 支持优先级
      x-message-ttl: 86400000     # 24小时过期
      x-max-length: 1000000       # 最大长度限制
      
  # 死信队列
  - name: tasks.dead_letter
    durable: true
    
  # 状态更新队列
  - name: status.updates
    durable: true

# 消息格式
TaskMessage:
  id: "msg_xxxxx"
  projectId: "project_xxxxx"
  batchId: "batch_xxxxx"
  url: "https://example.com/file.zip"
  priority: 5
  retryCount: 0
  ossConfig: {...}
```

## 四、API接口设计

### 4.1 项目管理接口

```yaml
# 创建项目
POST /api/projects
Body:
  {
    "name": "项目名称",
    "config": {
      "packSizeGB": 10,
      "ossConfig": {...},
      "concurrent": 5
    }
  }
Response:
  {
    "id": "project_xxxxx",
    "token": "Bearer token_xxxxx"
  }

# 获取项目列表
GET /api/projects
Response:
  {
    "projects": [...],
    "total": 10
  }

# 更新项目配置
PUT /api/projects/{id}
Body: { "config": {...} }

# 暂停项目
POST /api/projects/{id}/pause

# 恢复项目
POST /api/projects/{id}/resume

# 删除项目
DELETE /api/projects/{id}
```

### 4.2 任务管理接口

```yaml
# 批量提交任务
POST /api/tasks/batch
Headers:
  Authorization: Bearer {token}
Body:
  {
    "projectId": "project_xxxxx",
    "urls": [
      "https://example.com/file1.zip",
      "https://example.com/file2.zip",
      ...
    ],
    "priority": 5  # 可选，默认5
  }

# 获取任务统计
GET /api/tasks/stats/{projectId}
Response:
  {
    "total": 1000000,
    "pending": 800000,
    "downloading": 50000,
    "completed": 140000,
    "failed": 10000,
    "successRate": 93.33
  }
```

### 4.3 Worker管理接口

```yaml
# Worker注册
POST /api/workers/register
Body:
  {
    "name": "worker-01",
    "host": "************",
    "port": 8081,
    "capabilities": {...}
  }
Response:
  {
    "id": "worker_xxxxx",
    "token": "worker_token_xxxxx"
  }

# Worker心跳
POST /api/workers/heartbeat
Headers:
  Authorization: Bearer {worker_token}
Body:
  {
    "stats": {
      "cpuUsage": 45.5,
      "memoryUsage": 60.2,
      "activeTasks": 3
    }
  }

# 获取Worker列表
GET /api/workers

# 获取Worker详情
GET /api/workers/{id}
```

### 4.4 监控接口

```yaml
# 获取项目监控数据
GET /api/monitor/project/{id}
Response:
  {
    "stats": {...},
    "workers": [...],
    "queueDepth": 850000,
    "speedHistory": [...]
  }

# WebSocket实时监控
WS /api/monitor/realtime/{projectId}
Message:
  {
    "timestamp": 1705312800,
    "stats": {...},
    "workers": [...],
    "events": [...]
  }
```

## 五、核心功能实现

### 5.1 任务调度流程

```
1. 用户通过API提交批量任务URL
2. Server验证Token和项目状态
3. 创建任务批次记录
4. 将任务发送到RabbitMQ对应队列
5. 更新Redis统计信息
6. Worker从队列消费任务
7. Worker下载文件并累积
8. 达到打包阈值后压缩上传OSS
9. 更新统计信息和数据包记录
```

### 5.2 Worker工作机制

1. **任务消费**
   - 使用RabbitMQ的QoS控制并发
   - 失败任务不重试（避免队列堵塞）
   - 支持项目暂停/继续

2. **文件打包**
   - 按项目累积文件
   - 达到配置大小后触发打包
   - 使用tar.gz压缩
   - 打包过程不阻塞下载

3. **OSS上传**
   - 支持断点续传
   - 上传完成后删除本地文件
   - 记录上传信息到MongoDB

### 5.3 监控系统

1. **数据采集**
   - Worker定期上报状态（5秒）
   - Server聚合统计数据
   - 使用Redis缓存热数据

2. **实时推送**
   - WebSocket连接管理
   - 1秒推送一次数据
   - 自动重连机制

3. **历史数据**
   - Redis保存最近24小时
   - MongoDB存储汇总数据

## 六、部署方案

### 6.1 环境要求

```yaml
# 最小配置
Server:
  CPU: 4核
  内存: 8GB
  磁盘: 100GB SSD

Worker:
  CPU: 2核
  内存: 4GB
  磁盘: 500GB（取决于打包大小）
  网络: 100Mbps+

MongoDB:
  版本: 6.0+
  存储: 根据数据量估算

Redis:
  版本: 7.0+
  内存: 4GB+

RabbitMQ:
  版本: 3.11+
  内存: 4GB+
```

### 6.2 部署架构

```yaml
# 生产环境建议
- Server: 2个实例（高可用）
- Worker: 根据任务量动态扩展（5-50个）
- MongoDB: 副本集（1主2从）
- Redis: 主从模式
- RabbitMQ: 镜像队列集群（3节点）
```

### 6.3 Docker Compose配置

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:6
    command: --wiredTigerCacheSizeGB 2
    volumes:
      - mongo_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    
  redis:
    image: redis:7-alpine
    command: redis-server --maxmemory 4gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: password
      RABBITMQ_VM_MEMORY_HIGH_WATERMARK: 0.6
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "15672:15672"  # 管理界面
      
  server:
    build: ./server
    ports:
      - "8080:8080"
    environment:
      - MONGO_URI=**************************************
      - REDIS_ADDR=redis:6379
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672/
    depends_on:
      - mongodb
      - redis
      - rabbitmq
      
  worker:
    build: ./worker
    deploy:
      replicas: 5
    environment:
      - SERVER_URL=http://server:8080
      - WORK_DIR=/data/work
    volumes:
      - worker_data:/data/work
    depends_on:
      - server
      
  web:
    build: ./web
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8080
    depends_on:
      - server

volumes:
  mongo_data:
  redis_data:
  rabbitmq_data:
  worker_data:
```

## 七、开发指南

### 7.1 项目结构

```
download-scheduler/
├── server/                 # 调度服务器
│   ├── cmd/
│   │   └── server/        # 主程序入口
│   ├── internal/
│   │   ├── api/          # HTTP接口
│   │   ├── scheduler/    # 调度逻辑
│   │   ├── storage/      # 数据存储
│   │   ├── monitor/      # 监控模块
│   │   └── mq/          # 消息队列
│   ├── pkg/
│   │   ├── oss/         # OSS客户端
│   │   └── utils/       # 工具函数
│   └── configs/         # 配置文件
│
├── worker/              # Worker程序
│   ├── cmd/
│   │   └── worker/     # 主程序入口
│   ├── internal/
│   │   ├── downloader/ # 下载器
│   │   ├── packager/   # 打包器
│   │   └── uploader/   # 上传器
│   └── configs/
│
├── web/                # 前端项目
│   ├── pages/         # 页面
│   ├── components/    # 组件
│   ├── hooks/         # React Hooks
│   └── services/      # API服务
│
├── scripts/           # 部署脚本
├── docs/             # 文档
└── docker-compose.yml
```

### 7.2 关键依赖

```go
// Server端
github.com/gin-gonic/gin         // HTTP框架
github.com/gorilla/websocket     // WebSocket
go.mongodb.org/mongo-driver      // MongoDB驱动
github.com/redis/go-redis/v9     // Redis客户端
github.com/rabbitmq/amqp091-go   // RabbitMQ客户端

// Worker端
github.com/aliyun/aliyun-oss-go-sdk  // 阿里云OSS
github.com/aws/aws-sdk-go-v2         // AWS S3
github.com/minio/minio-go/v7         // MinIO
```

### 7.3 配置文件示例

```yaml
# server/configs/config.yaml
server:
  port: 8080
  mode: release  # debug|release
  
database:
  mongodb:
    uri: mongodb://localhost:27017
    database: download_scheduler
    timeout: 10s
  redis:
    addr: localhost:6379
    password: ""
    db: 0
    
mq:
  rabbitmq:
    url: amqp://guest:guest@localhost:5672/
    prefetch: 10
    
monitoring:
  enable: true
  stats_interval: 5s
  
log:
  level: info
  file: logs/server.log
  
# worker/configs/config.yaml  
worker:
  name: ${HOSTNAME}
  server_url: http://localhost:8080
  heartbeat_interval: 5s
  
download:
  concurrent: 5
  timeout: 300s
  retry: 3
  buffer_size: 1048576  # 1MB
  
packager:
  temp_dir: /data/temp
  compression: gzip
  
oss:
  upload_part_size: 5242880  # 5MB
  upload_concurrent: 3
```

## 八、注意事项

### 8.1 性能优化

1. **数据库优化**
   - 合理创建索引
   - 使用批量操作
   - 定期清理历史数据

2. **消息队列优化**
   - 设置合理的prefetch
   - 避免消息堆积
   - 监控队列深度

3. **网络优化**
   - 使用连接池
   - 启用HTTP/2
   - 合理设置超时

### 8.2 安全考虑

1. **认证授权**
   - API Token验证
   - Worker注册认证
   - 项目隔离

2. **数据安全**
   - OSS密钥加密存储
   - HTTPS传输（生产环境）
   - 敏感日志脱敏

### 8.3 运维建议

1. **监控告警**
   - 队列积压告警
   - Worker离线告警
   - 磁盘空间告警

2. **日志管理**
   - 集中日志收集
   - 日志轮转策略
   - 错误日志分析

3. **备份恢复**
   - MongoDB定期备份
   - 配置文件备份
   - 灾难恢复预案

## 九、扩展计划

### 9.1 功能扩展
- 支持更多存储后端（FTP、HDFS等）
- 任务优先级动态调整
- 智能负载均衡
- 断点续传优化

### 9.2 性能扩展
- 分布式Server集群
- 任务分片处理
- 缓存优化
- 预测性资源调度

