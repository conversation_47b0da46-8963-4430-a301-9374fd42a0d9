# 分布式下载调度系统 API 文档

## 概述

本文档描述了分布式下载调度系统的所有API接口，包括项目管理、任务管理、Worker管理和监控接口。

**基础URL**: `http://localhost:8080/api`

## 认证方式

### 项目API认证
使用Bearer Token认证，在请求头中添加：
```
Authorization: Bearer_xxxxxxxxxxxxxxxx
```

### Worker API认证
使用Worker Token认证，在请求头中添加：
```
Authorization: Bearer worker_xxxxxxxxxxxxxxxx
```

## API接口

### 1. 项目管理接口

#### 1.1 创建项目

**接口**: `POST /api/projects`

**描述**: 创建新的下载项目

**请求体**:
```json
{
  "name": "项目名称",
  "config": {
    "packSizeGB": 10,
    "ossConfig": {
      "provider": "aliyun",
      "endpoint": "oss-cn-beijing.aliyuncs.com",
      "bucket": "download-data",
      "accessKey": "your_access_key",
      "secretKey": "your_secret_key",
      "prefix": "project1/",
      "region": "cn-beijing"
    },
    "concurrent": 5,
    "retryTimes": 3,
    "downloadTimeout": 300
  }
}
```

**响应**:
```json
{
  "id": "project_id_hex",
  "token": "Bearer_xxxxxxxxxxxxxxxx"
}
```

**状态码**:
- `200`: 成功
- `400`: 请求参数错误
- `500`: 服务器内部错误

---

#### 1.2 获取项目列表

**接口**: `GET /api/projects`

**描述**: 获取所有项目列表

**响应**:
```json
{
  "projects": [
    {
      "id": "project_id_hex",
      "name": "项目名称",
      "token": "Bearer_xxxxxxxxxxxxxxxx",
      "config": {
        "packSizeGB": 10,
        "ossConfig": {...},
        "concurrent": 5,
        "retryTimes": 3,
        "downloadTimeout": 300
      },
      "status": "active",
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z"
    }
  ],
  "total": 1
}
```

---

#### 1.3 获取项目详情

**接口**: `GET /api/projects/:id`

**描述**: 根据ID获取项目详细信息

**路径参数**:
- `id`: 项目ID

**响应**:
```json
{
  "id": "project_id_hex",
  "name": "项目名称",
  "token": "Bearer_xxxxxxxxxxxxxxxx",
  "config": {...},
  "status": "active",
  "createdAt": "2024-01-15T10:00:00Z",
  "updatedAt": "2024-01-15T10:00:00Z"
}
```

**状态码**:
- `200`: 成功
- `400`: 项目ID格式错误
- `404`: 项目不存在

---

#### 1.4 更新项目

**接口**: `PUT /api/projects/:id`

**描述**: 更新项目配置

**路径参数**:
- `id`: 项目ID

**请求体**:
```json
{
  "name": "新项目名称",
  "config": {
    "packSizeGB": 20,
    "concurrent": 8
  }
}
```

**响应**:
```json
{
  "message": "Project updated successfully"
}
```

---

#### 1.5 删除项目

**接口**: `DELETE /api/projects/:id`

**描述**: 删除项目

**路径参数**:
- `id`: 项目ID

**响应**:
```json
{
  "message": "Project deleted successfully"
}
```

---

#### 1.6 暂停项目

**接口**: `POST /api/projects/:id/pause`

**描述**: 暂停项目，停止处理新任务

**路径参数**:
- `id`: 项目ID

**响应**:
```json
{
  "message": "Project paused successfully"
}
```

---

#### 1.7 恢复项目

**接口**: `POST /api/projects/:id/resume`

**描述**: 恢复项目，继续处理任务

**路径参数**:
- `id`: 项目ID

**响应**:
```json
{
  "message": "Project resumed successfully"
}
```

---

### 2. 任务管理接口

#### 2.1 批量提交任务

**接口**: `POST /api/tasks/batch`

**认证**: 需要项目Token

**描述**: 批量提交下载任务

**请求头**:
```
Authorization: Bearer_xxxxxxxxxxxxxxxx
```

**请求体**:
```json
{
  "projectId": "project_id_hex",
  "requests": [
    {
      "url": "https://example.com/file1.zip",
      "method": "GET",
      "headers": {
        "User-Agent": "MyDownloader/1.0",
        "Authorization": "Bearer token123"
      }
    },
    {
      "url": "https://example.com/file2.pdf",
      "method": "POST",
      "headers": {
        "Content-Type": "application/json"
      }
    },
    {
      "url": "https://example.com/file3.mp4"
    }
  ],
  "priority": 5
}
```

**响应**:
```json
{
  "message": "Tasks submitted successfully",
  "totalTasks": 3,
  "publishedTasks": 3,
  "batchId": "batch_id_hex"
}
```

**状态码**:
- `200`: 成功
- `400`: 请求参数错误或项目已暂停
- `401`: 认证失败
- `500`: 服务器内部错误

---

#### 2.2 获取任务统计

**接口**: `GET /api/tasks/stats/:projectId`

**描述**: 获取项目任务统计信息

**路径参数**:
- `projectId`: 项目ID

**响应**:
```json
{
  "totalTasks": 1000000,
  "pendingTasks": 800000,
  "downloadingTasks": 50000,
  "completedTasks": 140000,
  "failedTasks": 10000,
  "totalSize": 1099511627776,
  "downloadSpeed": 104857600,
  "successRate": 93.33,
  "lastUpdate": 1705312800
}
```

---

### 3. Worker管理接口

#### 3.1 Worker预注册

**接口**: `POST /api/workers/register`

**描述**: 预注册新的Worker节点，生成注册Token

**请求体**:
```json
{
  "name": "worker-01",
  "description": "GPU服务器Worker节点",
  "capabilities": {
    "maxConcurrent": 10,
    "diskSpace": 500,
    "bandwidth": 1000
  }
}
```

**响应**:
```json
{
  "id": "worker_id_hex",
  "token": "worker_xxxxxxxxxxxxxxxx",
  "message": "Worker预注册成功，请使用token启动Worker服务"
}
```

**状态码**:
- `200`: 成功
- `400`: 请求参数错误
- `500`: 服务器内部错误

---

#### 3.2 Worker激活注册

**接口**: `POST /api/workers/activate`

**认证**: 需要Worker Token

**描述**: Worker服务启动后的激活注册，上报实际运行信息

**请求头**:
```
Authorization: Bearer worker_xxxxxxxxxxxxxxxx
```

**请求体**:
```json
{
  "host": "************",
  "port": 8081,
  "version": "1.0.0",
  "systemInfo": {
    "os": "linux",
    "arch": "amd64",
    "cpuCores": 8,
    "totalMemory": 16384,
    "totalDisk": 1000
  }
}
```

**响应**:
```json
{
  "message": "Worker激活成功",
  "workerId": "worker_id_hex",
  "assignedProjects": []
}
```

**状态码**:
- `200`: 激活成功
- `401`: Token无效或过期
- `409`: Worker已激活
- `500`: 服务器内部错误

---

#### 3.3 Worker心跳

**接口**: `POST /api/workers/heartbeat`

**认证**: 需要Worker Token

**描述**: Worker定期发送心跳和状态信息

**请求头**:
```
Authorization: Bearer worker_xxxxxxxxxxxxxxxx
```

**请求体**:
```json
{
  "stats": {
    "cpuUsage": 45.5,
    "memoryUsage": 60.2,
    "diskUsage": 30.5,
    "activeTasks": 3,
    "totalProcessed": 150000,
    "totalFailed": 500,
    "networkSpeed": 1000
  },
  "health": {
    "status": "healthy",
    "uptime": 86400,
    "lastError": null
  }
}
```

**响应**:
```json
{
  "message": "Heartbeat received",
  "assignments": {
    "newProjects": [],
    "removedProjects": []
  }
}
```

---

#### 3.4 获取Worker列表

**接口**: `GET /api/workers`

**描述**: 获取所有Worker列表

**响应**:
```json
{
  "workers": [
    {
      "id": "worker_id_hex",
      "name": "worker-01",
      "description": "GPU服务器Worker节点",
      "status": "active",
      "host": "************",
      "port": 8081,
      "version": "1.0.0",
      "capabilities": {
        "maxConcurrent": 10,
        "diskSpace": 500,
        "bandwidth": 1000
      },
      "systemInfo": {
        "os": "linux",
        "arch": "amd64",
        "cpuCores": 8,
        "totalMemory": 16384,
        "totalDisk": 1000
      },
      "stats": {
        "cpuUsage": 45.5,
        "memoryUsage": 60.2,
        "diskUsage": 30.5,
        "activeTasks": 3,
        "totalProcessed": 150000,
        "totalFailed": 500
      },
      "health": {
        "status": "healthy",
        "uptime": 86400,
        "lastError": null
      },
      "projects": [],
      "lastHeartbeat": "2024-01-15T10:30:00Z",
      "createdAt": "2024-01-15T10:00:00Z",
      "activatedAt": "2024-01-15T10:05:00Z"
    }
  ],
  "total": 1
}
```

**Worker状态说明**:
- `pending`: 已预注册，等待激活
- `active`: 已激活，正常工作
- `offline`: 离线（心跳超时）
- `error`: 错误状态
- `disabled`: 已禁用

---

#### 3.5 获取Worker详情

**接口**: `GET /api/workers/:id`

**描述**: 根据ID获取Worker详细信息

**路径参数**:
- `id`: Worker ID

**响应**: 同获取Worker列表中的单个Worker对象

---

#### 3.6 更新Worker配置

**接口**: `PUT /api/workers/:id`

**描述**: 更新Worker的配置信息（名称、描述、能力等）

**路径参数**:
- `id`: Worker ID

**请求体**:
```json
{
  "name": "worker-01-updated",
  "description": "更新后的描述",
  "capabilities": {
    "maxConcurrent": 15,
    "diskSpace": 1000,
    "bandwidth": 2000
  }
}
```

**响应**:
```json
{
  "message": "Worker配置更新成功"
}
```

---

#### 3.7 禁用/启用Worker

**接口**: `POST /api/workers/:id/disable` 或 `POST /api/workers/:id/enable`

**描述**: 禁用或启用Worker节点

**路径参数**:
- `id`: Worker ID

**响应**:
```json
{
  "message": "Worker已禁用/启用"
}
```

---

#### 3.8 删除Worker

**接口**: `DELETE /api/workers/:id`

**描述**: 删除Worker节点（仅限已离线的Worker）

**路径参数**:
- `id`: Worker ID

**响应**:
```json
{
  "message": "Worker删除成功"
}
```

---

## Worker部署流程

### 1. 管理员操作
1. 在管理界面创建Worker预注册
2. 获得Worker Token
3. 将Token提供给运维人员

### 2. 运维人员操作  
1. 在目标服务器上部署Worker程序
2. 使用Token启动Worker服务
   ```bash
   ./worker --token=worker_xxxxxxxxxxxxxxxx --port=8081
   ```
3. Worker自动连接并上报系统信息

### 3. 系统自动处理
1. 验证Token有效性
2. 激活Worker注册
3. 记录实际的host、port、version等信息
4. 开始接收心跳和任务分配

### 使用示例

```bash
# 1. 管理员预注册Worker
curl -X POST http://localhost:8080/api/workers/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "hk-worker-01",
    "description": "香港的服务器",
    "capabilities": {
      "maxConcurrent": 20,
      "diskSpace": 2000,
      "bandwidth": 10000
    }
  }'

# 响应: {"id": "xxx", "token": "worker_abc123", "message": "..."}

# 2. 在目标服务器启动Worker
./worker --token=worker_abc123 --port=8081

# 3. Worker自动激活（由Worker程序发送）
curl -X POST http://localhost:8080/api/workers/activate \
  -H "Authorization: Bearer worker_abc123" \
  -H "Content-Type: application/json" \
  -d '{
    "host": "************0",
    "port": 8081,
    "version": "1.2.0",
    "systemInfo": {
      "os": "linux",
      "arch": "amd64", 
      "cpuCores": 16,
      "totalMemory": 32768,
      "totalDisk": 2000
    }
  }'
```

### 4. 监控接口

#### 4.1 获取项目监控数据

**接口**: `GET /api/monitor/project/:id`

**描述**: 获取项目完整的监控数据

**路径参数**:
- `id`: 项目ID

**响应**:
```json
{
  "stats": {
    "totalTasks": 1000000,
    "pendingTasks": 800000,
    "downloadingTasks": 50000,
    "completedTasks": 140000,
    "failedTasks": 10000,
    "totalSize": 1099511627776,
    "downloadSpeed": 104857600,
    "successRate": 93.33,
    "lastUpdate": 1705312800
  },
  "workers": [
    {
      "id": "worker_id_hex",
      "name": "worker-01",
      "status": "active",
      "stats": {...}
    }
  ],
  "queueDepth": 850000,
  "speedHistory": [
    {
      "timestamp": 1705312800,
      "speed": 104857600
    }
  ],
  "workerStats": {
    "worker_id_hex": {
      "cpu_usage": "45.5",
      "memory_usage": "60.2",
      "active_tasks": "3"
    }
  }
}
```

---

#### 4.2 实时监控WebSocket

**接口**: `WS /api/monitor/realtime/:projectId`

**描述**: WebSocket连接，实时推送监控数据

**路径参数**:
- `projectId`: 项目ID

**WebSocket消息格式**:
```json
{
  "timestamp": 1705312800,
  "stats": {...},
  "workers": [...],
  "queueDepth": 850000,
  "workerStats": {...},
  "events": []
}
```

**连接示例**:
```javascript
const ws = new WebSocket('ws://localhost:8080/api/monitor/realtime/project_id_hex');

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Monitoring data:', data);
};
```

---

### 5. 健康检查接口

#### 5.1 健康检查

**接口**: `GET /ping`

**描述**: 服务健康检查

**响应**:
```json
{
  "message": "pong",
  "status": "healthy"
}
```

---

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 错误响应格式

```json
{
  "error": "错误描述信息"
}
```

## 数据类型说明

### 下载请求 (DownloadRequest)
```json
{
  "url": "https://example.com/file.zip",         // 必填：下载URL
  "method": "GET",                               // 可选：HTTP方法，默认为GET
  "headers": {                                   // 可选：自定义请求头
    "User-Agent": "MyDownloader/1.0",
    "Authorization": "Bearer token123",
    "Referer": "https://example.com"
  }
}
```

### OSS配置 (OSSConfig)
```json
{
  "provider": "aliyun|aws|minio",
  "endpoint": "OSS端点",
  "bucket": "存储桶名称",
  "accessKey": "访问密钥",
  "secretKey": "密钥",
  "prefix": "路径前缀",
  "region": "地域",
  "forcePathStyle": false  // 强制使用路径样式，MinIO需要此选项
}
```

### Worker能力 (WorkerCapabilities)
```json
{
  "maxConcurrent": 10,  // 最大并发数
  "diskSpace": 500,     // 磁盘空间(GB)
  "bandwidth": 1000     // 带宽(Mbps)
}
```

### Worker统计 (WorkerStats)
```json
{
  "cpuUsage": 45.5,        // CPU使用率(%)
  "memoryUsage": 60.2,     // 内存使用率(%)
  "diskUsage": 30.5,       // 磁盘使用率(%)
  "activeTasks": 3,        // 活跃任务数
  "totalProcessed": 150000, // 总处理数
  "totalFailed": 500       // 总失败数
}
```

## 使用示例

### 创建项目并提交任务

```bash
# 1. 创建项目
curl -X POST http://localhost:8080/api/projects \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试项目",
    "config": {
      "packSizeGB": 10,
      "ossConfig": {
        "provider": "aliyun",
        "endpoint": "oss-cn-beijing.aliyuncs.com",
        "bucket": "test-bucket",
        "accessKey": "your_access_key",
        "secretKey": "your_secret_key",
        "prefix": "test/",
        "region": "cn-beijing"
      },
      "concurrent": 5,
      "retryTimes": 3,
      "downloadTimeout": 300
    }
  }'

# 2. 提交任务（使用返回的token）
curl -X POST http://localhost:8080/api/tasks/batch \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer_xxxxxxxxxxxxxxxx" \
  -d '{
    "projectId": "project_id_hex",
    "requests": [
      {
        "url": "https://example.com/file1.zip",
        "method": "GET",
        "headers": {
          "User-Agent": "MyDownloader/1.0"
        }
      },
      {
        "url": "https://example.com/file2.pdf",
        "method": "POST",
        "headers": {
          "Content-Type": "application/json",
          "Authorization": "Bearer api-key"
        }
      }
    ],
    "priority": 5
  }'

# 3. 查看任务统计
curl http://localhost:8080/api/tasks/stats/project_id_hex
```

### Worker注册和心跳

```bash
# 1. 管理员预注册Worker
curl -X POST http://localhost:8080/api/workers/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "hk-worker-01",
    "description": "香港的服务器",
    "capabilities": {
      "maxConcurrent": 20,
      "diskSpace": 2000,
      "bandwidth": 10000
    }
  }'

# 响应: {"id": "xxx", "token": "worker_abc123", "message": "..."}

# 2. 在目标服务器启动Worker
./worker --token=worker_abc123 --port=8081

# 3. Worker自动激活（由Worker程序发送）
curl -X POST http://localhost:8080/api/workers/activate \
  -H "Authorization: Bearer worker_abc123" \
  -H "Content-Type: application/json" \
  -d '{
    "host": "************0",
    "port": 8081,
    "version": "1.2.0",
    "systemInfo": {
      "os": "linux",
      "arch": "amd64", 
      "cpuCores": 16,
      "totalMemory": 32768,
      "totalDisk": 2000
    }
  }'
```

## 注意事项

1. **认证Token**: 项目Token和Worker Token格式不同，请注意区分使用
2. **并发限制**: API接口有并发限制，建议合理控制请求频率
3. **数据格式**: 所有时间字段使用ISO 8601格式
4. **文件大小**: 所有大小相关字段使用字节为单位
5. **WebSocket**: 实时监控WebSocket每秒推送一次数据，注意处理连接断开重连
6. **项目状态**: 暂停状态的项目不会处理新任务，但已在处理的任务会继续完成

## 后端实现建议

为了适配新的Worker预注册流程，后端需要进行以下修改：

### 1. 数据库模型更新

```go
type Worker struct {
    ID           primitive.ObjectID `bson:"_id,omitempty" json:"id"`
    Name         string            `bson:"name" json:"name"`
    Description  string            `bson:"description" json:"description"`
    Status       string            `bson:"status" json:"status"` // pending, active, offline, error, disabled
    
    // 预注册信息
    Token        string            `bson:"token" json:"token,omitempty"`
    Capabilities WorkerCapabilities `bson:"capabilities" json:"capabilities"`
    
    // 激活后信息
    Host         string            `bson:"host,omitempty" json:"host,omitempty"`
    Port         int               `bson:"port,omitempty" json:"port,omitempty"`
    Version      string            `bson:"version,omitempty" json:"version,omitempty"`
    SystemInfo   *SystemInfo       `bson:"systemInfo,omitempty" json:"systemInfo,omitempty"`
    
    // 运行时信息
    Stats        *WorkerStats      `bson:"stats,omitempty" json:"stats,omitempty"`
    Health       *HealthInfo       `bson:"health,omitempty" json:"health,omitempty"`
    Projects     []string          `bson:"projects" json:"projects"`
    
    // 时间信息
    CreatedAt     time.Time        `bson:"createdAt" json:"createdAt"`
    ActivatedAt   *time.Time       `bson:"activatedAt,omitempty" json:"activatedAt,omitempty"`
    LastHeartbeat *time.Time       `bson:"lastHeartbeat,omitempty" json:"lastHeartbeat,omitempty"`
}

type SystemInfo struct {
    OS          string `bson:"os" json:"os"`
    Arch        string `bson:"arch" json:"arch"`
    CPUCores    int    `bson:"cpuCores" json:"cpuCores"`
    TotalMemory int64  `bson:"totalMemory" json:"totalMemory"`
    TotalDisk   int64  `bson:"totalDisk" json:"totalDisk"`
}

type HealthInfo struct {
    Status    string `bson:"status" json:"status"`
    Uptime    int64  `bson:"uptime" json:"uptime"`
    LastError string `bson:"lastError,omitempty" json:"lastError,omitempty"`
}
```

### 2. API 接口实现

#### 预注册接口
```go
func (h *WorkerHandler) RegisterWorker(c *gin.Context) {
    var req struct {
        Name         string            `json:"name" binding:"required"`
        Description  string            `json:"description"`
        Capabilities WorkerCapabilities `json:"capabilities" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    // 生成 Worker Token
    token := generateWorkerToken()
    
    worker := Worker{
        ID:           primitive.NewObjectID(),
        Name:         req.Name,
        Description:  req.Description,
        Status:       "pending",
        Token:        token,
        Capabilities: req.Capabilities,
        Projects:     []string{},
        CreatedAt:    time.Now(),
    }
    
    // 保存到数据库
    if err := h.workerService.CreateWorker(&worker); err != nil {
        c.JSON(500, gin.H{"error": "Failed to create worker"})
        return
    }
    
    c.JSON(200, gin.H{
        "id":      worker.ID.Hex(),
        "token":   token,
        "message": "Worker预注册成功，请使用token启动Worker服务",
    })
}
```

#### 激活接口
```go
func (h *WorkerHandler) ActivateWorker(c *gin.Context) {
    token := extractWorkerToken(c)
    if token == "" {
        c.JSON(401, gin.H{"error": "Invalid token"})
        return
    }
    
    var req struct {
        Host       string     `json:"host" binding:"required"`
        Port       int        `json:"port" binding:"required"`
        Version    string     `json:"version" binding:"required"`
        SystemInfo SystemInfo `json:"systemInfo" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    // 查找对应的 Worker
    worker, err := h.workerService.GetWorkerByToken(token)
    if err != nil {
        c.JSON(401, gin.H{"error": "Invalid token"})
        return
    }
    
    if worker.Status != "pending" {
        c.JSON(409, gin.H{"error": "Worker already activated"})
        return
    }
    
    // 更新激活信息
    now := time.Now()
    updates := bson.M{
        "status":       "active",
        "host":         req.Host,
        "port":         req.Port,
        "version":      req.Version,
        "systemInfo":   req.SystemInfo,
        "activatedAt":  &now,
        "lastHeartbeat": &now,
    }
    
    if err := h.workerService.UpdateWorker(worker.ID, updates); err != nil {
        c.JSON(500, gin.H{"error": "Failed to activate worker"})
        return
    }
    
    c.JSON(200, gin.H{
        "message":          "Worker激活成功",
        "workerId":         worker.ID.Hex(),
        "assignedProjects": []string{},
    })
}
```

### 3. 心跳接口更新

```go
func (h *WorkerHandler) Heartbeat(c *gin.Context) {
    token := extractWorkerToken(c)
    worker, err := h.workerService.GetWorkerByToken(token)
    if err != nil {
        c.JSON(401, gin.H{"error": "Invalid token"})
        return
    }
    
    var req struct {
        Stats  WorkerStats `json:"stats" binding:"required"`
        Health HealthInfo  `json:"health"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    // 更新状态和心跳时间
    now := time.Now()
    updates := bson.M{
        "stats":         req.Stats,
        "health":        req.Health,
        "lastHeartbeat": &now,
        "status":        "active", // 确保状态为活跃
    }
    
    if err := h.workerService.UpdateWorker(worker.ID, updates); err != nil {
        c.JSON(500, gin.H{"error": "Failed to update heartbeat"})
        return
    }
    
    // 返回任务分配信息
    c.JSON(200, gin.H{
        "message": "Heartbeat received",
        "assignments": gin.H{
            "newProjects":     []string{},
            "removedProjects": []string{},
        },
    })
}
```

### 4. 状态检查定时任务

```go
func (s *WorkerService) CheckWorkerStatus() {
    // 每30秒运行一次
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            // 检查超时的 Worker
            timeout := time.Now().Add(-2 * time.Minute) // 2分钟无心跳认为离线
            
            filter := bson.M{
                "status": "active",
                "$or": []bson.M{
                    {"lastHeartbeat": bson.M{"$lt": timeout}},
                    {"lastHeartbeat": nil},
                },
            }
            
            update := bson.M{
                "$set": bson.M{
                    "status": "offline",
                },
            }
            
            s.workerRepo.UpdateMany(filter, update)
        }
    }
}
```

### 5. Token 生成和验证

```go
func generateWorkerToken() string {
    return "worker_" + generateRandomString(32)
}

func extractWorkerToken(c *gin.Context) string {
    auth := c.GetHeader("Authorization")
    if !strings.HasPrefix(auth, "Bearer worker_") {
        return ""
    }
    return strings.TrimPrefix(auth, "Bearer ")
}
```

这样修改后，整个流程将更加合理：
1. 管理员在前端创建Worker预注册，获得Token
2. 运维人员使用Token启动Worker服务
3. Worker自动连接并激活注册，上报真实的系统信息
4. 系统开始监控Worker状态和分配任务

## 版本信息

- **API版本**: v1.0
- **文档版本**: 1.0.0
- **最后更新**: 2024-01-15 

# API 文档 - WebSocket 版本

## 重要变更说明

### WebSocket 连接机制

系统已升级为基于 WebSocket 的实时连接架构，替代了原有的 HTTP 心跳机制：

- **Worker 连接**：Worker 通过 WebSocket 连接到 Server，实现真正的实时通信
- **状态实时性**：Worker 状态基于 WebSocket 连接状态实时计算，无延迟
- **消息推送**：Server 可向 Worker 实时推送配置更新、控制命令等

## Worker 管理接口

### WebSocket 连接端点

```yaml
# Worker WebSocket 连接
WS /api/workers/connect
Headers:
  Authorization: Bearer {worker_token}
消息格式: JSON
连接后自动处理激活、统计上报等
```

### Worker 激活（WebSocket）

```yaml
# WebSocket 消息格式 - Worker 激活
{
  "type": "activate",
  "data": {
    "host": "************",
    "port": 8081,
    "version": "1.0.0",
    "systemInfo": {
      "os": "linux",
      "arch": "amd64",
      "cpuCores": 8,
      "memoryGB": 16,
      "diskSpaceGB": 500,
      "maxBandwidth": 1000
    }
  }
}

# 服务器响应
{
  "type": "activate_ack",
  "data": {
    "message": "Worker activated successfully",
    "workerId": "worker_xxxxx",
    "assignedProjects": []
  }
}
```

### Worker 统计上报（WebSocket）

```yaml
# WebSocket 消息格式 - 统计上报
{
  "type": "stats",
  "data": {
    "stats": {
      "cpuUsage": 45.5,
      "memoryUsage": 60.2,
      "diskUsage": 30.5,
      "activeTasks": 3,
      "totalProcessed": 1500,
      "totalFailed": 10,
      "networkSpeed": 52428800
    },
    "health": {
      "status": "healthy",
      "uptime": 3600,
      "lastError": ""
    }
  }
}
```

### 向 Worker 发送消息

```yaml
POST /api/workers/{id}/message
Headers:
  Authorization: Bearer {user_token}
Body:
  {
    "type": "config",  # 消息类型：config|control|task
    "data": {
      "action": "update_config",
      "config": {...}
    }
  }
Response:
  {
    "message": "Message sent successfully",
    "worker": "worker-01",
    "type": "config"
  }
```

### 广播消息到所有 Worker

```yaml
POST /api/workers/broadcast
Headers:
  Authorization: Bearer {user_token}
Body:
  {
    "type": "control",
    "data": {
      "action": "pause_all",
      "reason": "System maintenance"
    }
  }
Response:
  {
    "message": "Message broadcasted successfully",
    "type": "control",
    "workers": 5
  }
```

### Worker 列表（实时状态）

```yaml
GET /api/workers
Headers:
  Authorization: Bearer {user_token}
Response:
  {
    "workers": [
      {
        "id": "worker_xxxxx",
        "name": "worker-01",
        "status": "active",      # 实时计算状态
        "isConnected": true,     # WebSocket 连接状态
        "stats": {              # 实时统计信息
          "cpuUsage": 45.5,
          "memoryUsage": 60.2,
          "activeTasks": 3
        },
        "health": {             # 实时健康状态
          "status": "healthy",
          "uptime": 3600
        }
      }
    ],
    "total": 1
  }
```

### Worker 详情（实时状态）

```yaml
GET /api/workers/{id}
Headers:
  Authorization: Bearer {user_token}
Response:
  {
    "id": "worker_xxxxx",
    "name": "worker-01",
    "status": "active",        # 实时状态
    "isConnected": true,       # 连接状态
    "stats": {...},           # 实时统计
    "health": {...},          # 实时健康状态
    "host": "************",
    "port": 8081,
    "capabilities": {...}
  }
```

## WebSocket 消息类型

### Worker -> Server

| 消息类型 | 说明 | 数据格式 |
|---------|------|----------|
| activate | Worker 激活请求 | ActivateData |
| stats | 统计信息上报 | StatsData |
| ping | 心跳检测 | 无 |

### Server -> Worker

| 消息类型 | 说明 | 数据格式 |
|---------|------|----------|
| activate_ack | 激活确认 | {message, workerId} |
| config | 配置更新 | 配置对象 |
| control | 控制命令 | {action, ...} |
| pong | 心跳响应 | {timestamp} |

## 状态说明

### Worker 状态

- **pending**: Worker 已注册但未建立 WebSocket 连接
- **active**: WebSocket 连接正常，Worker 运行中
- **offline**: WebSocket 连接断开，Worker 离线

### 优势

1. **实时性**: 状态变更立即感知，无需等待心跳超时
2. **高效性**: 减少 HTTP 轮询，降低网络开销
3. **可靠性**: 连接断开立即检测，状态更准确
4. **扩展性**: 支持实时消息推送，便于功能扩展

## 兼容性说明

- HTTP 心跳接口保留作为兼容性 API
- 建议使用 WebSocket 连接获得最佳体验
- 旧版本 Worker 仍可通过 HTTP 接口工作 