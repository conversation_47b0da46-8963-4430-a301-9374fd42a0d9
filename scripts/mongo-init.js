// MongoDB 初始化脚本
db = db.getSiblingDB('download_scheduler_dev');

// 创建用户
db.createUser({
    user: 'app_user',
    pwd: 'app_password',
    roles: [
        {
            role: 'readWrite',
            db: 'download_scheduler_dev'
        }
    ]
});

// 创建基础集合和索引
db.createCollection('projects');
db.createCollection('tasks');
db.createCollection('workers');
db.createCollection('users');

// 创建索引
db.projects.createIndex({ "name": 1 }, { unique: true });
db.projects.createIndex({ "created_at": -1 });
db.projects.createIndex({ "status": 1 });

db.tasks.createIndex({ "project_id": 1 });
db.tasks.createIndex({ "status": 1 });
db.tasks.createIndex({ "created_at": -1 });
db.tasks.createIndex({ "worker_id": 1 });

db.workers.createIndex({ "name": 1 }, { unique: true });
db.workers.createIndex({ "status": 1 });
db.workers.createIndex({ "last_heartbeat": -1 });

db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "email": 1 }, { unique: true });

print('MongoDB 初始化完成');
