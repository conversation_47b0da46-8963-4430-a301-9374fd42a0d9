#!/bin/bash

# Download Scheduler 中间件设置脚本
# 用于启动和配置开发环境所需的中间件服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose 是否安装
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查 Docker 服务是否运行
check_docker_service() {
    log_info "检查 Docker 服务状态..."
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
    
    log_success "Docker 服务正常运行"
}

# 创建必要的配置文件
create_config_files() {
    log_info "创建配置文件..."
    
    # 创建 MongoDB 初始化脚本
    cat > scripts/mongo-init.js << 'EOF'
// MongoDB 初始化脚本
db = db.getSiblingDB('download_scheduler_dev');

// 创建用户
db.createUser({
    user: 'app_user',
    pwd: 'app_password',
    roles: [
        {
            role: 'readWrite',
            db: 'download_scheduler_dev'
        }
    ]
});

// 创建基础集合和索引
db.createCollection('projects');
db.createCollection('tasks');
db.createCollection('workers');
db.createCollection('users');

// 创建索引
db.projects.createIndex({ "name": 1 }, { unique: true });
db.projects.createIndex({ "created_at": -1 });
db.projects.createIndex({ "status": 1 });

db.tasks.createIndex({ "project_id": 1 });
db.tasks.createIndex({ "status": 1 });
db.tasks.createIndex({ "created_at": -1 });
db.tasks.createIndex({ "worker_id": 1 });

db.workers.createIndex({ "name": 1 }, { unique: true });
db.workers.createIndex({ "status": 1 });
db.workers.createIndex({ "last_heartbeat": -1 });

db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "email": 1 }, { unique: true });

print('MongoDB 初始化完成');
EOF

    # 创建 RabbitMQ 配置文件
    cat > scripts/rabbitmq.conf << 'EOF'
# RabbitMQ 配置文件
management.tcp.port = 15672
management.tcp.ip = 0.0.0.0

# 启用管理插件
management.load_definitions = /etc/rabbitmq/definitions.json

# 内存和磁盘限制
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0

# 日志配置
log.console = true
log.console.level = info

# 集群配置（开发环境单节点）
cluster_formation.peer_discovery_backend = classic_config
EOF

    # 创建 RabbitMQ 队列定义
    cat > scripts/rabbitmq-definitions.json << 'EOF'
{
    "rabbit_version": "3.11.0",
    "rabbitmq_version": "3.11.0",
    "product_name": "RabbitMQ",
    "product_version": "3.11.0",
    "users": [
        {
            "name": "admin",
            "password_hash": "password123",
            "hashing_algorithm": "rabbit_password_hashing_sha256",
            "tags": "administrator"
        }
    ],
    "vhosts": [
        {
            "name": "/"
        }
    ],
    "permissions": [
        {
            "user": "admin",
            "vhost": "/",
            "configure": ".*",
            "write": ".*",
            "read": ".*"
        }
    ],
    "topic_permissions": [],
    "parameters": [],
    "global_parameters": [
        {
            "name": "cluster_name",
            "value": "download-scheduler-dev"
        }
    ],
    "policies": [],
    "queues": [
        {
            "name": "download_tasks",
            "vhost": "/",
            "durable": true,
            "auto_delete": false,
            "arguments": {
                "x-message-ttl": 3600000,
                "x-max-length": 10000
            }
        },
        {
            "name": "worker_heartbeat",
            "vhost": "/",
            "durable": true,
            "auto_delete": false,
            "arguments": {
                "x-message-ttl": 60000
            }
        }
    ],
    "exchanges": [
        {
            "name": "download_scheduler",
            "vhost": "/",
            "type": "topic",
            "durable": true,
            "auto_delete": false,
            "internal": false,
            "arguments": {}
        }
    ],
    "bindings": [
        {
            "source": "download_scheduler",
            "vhost": "/",
            "destination": "download_tasks",
            "destination_type": "queue",
            "routing_key": "task.download",
            "arguments": {}
        },
        {
            "source": "download_scheduler",
            "vhost": "/",
            "destination": "worker_heartbeat",
            "destination_type": "queue",
            "routing_key": "worker.heartbeat",
            "arguments": {}
        }
    ]
}
EOF

    log_success "配置文件创建完成"
}

# 启动中间件服务
start_services() {
    log_info "启动中间件服务..."
    
    # 停止可能存在的旧容器
    docker-compose down 2>/dev/null || true
    
    # 启动核心中间件服务
    docker-compose up -d mongodb redis rabbitmq
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务健康状态
    check_service_health
}

# 检查服务健康状态
check_service_health() {
    log_info "检查服务健康状态..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "健康检查尝试 $attempt/$max_attempts"
        
        # 检查 MongoDB
        if docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" &>/dev/null; then
            log_success "MongoDB 服务正常"
            mongodb_ready=true
        else
            mongodb_ready=false
        fi
        
        # 检查 Redis
        if docker-compose exec -T redis redis-cli -a password123 ping &>/dev/null; then
            log_success "Redis 服务正常"
            redis_ready=true
        else
            redis_ready=false
        fi
        
        # 检查 RabbitMQ
        if docker-compose exec -T rabbitmq rabbitmq-diagnostics ping &>/dev/null; then
            log_success "RabbitMQ 服务正常"
            rabbitmq_ready=true
        else
            rabbitmq_ready=false
        fi
        
        if [ "$mongodb_ready" = true ] && [ "$redis_ready" = true ] && [ "$rabbitmq_ready" = true ]; then
            log_success "所有服务健康检查通过"
            return 0
        fi
        
        sleep 5
        ((attempt++))
    done
    
    log_error "服务健康检查失败，请检查日志"
    docker-compose logs
    exit 1
}

# 显示服务信息
show_service_info() {
    log_info "服务访问信息："
    echo ""
    echo "MongoDB:"
    echo "  - 地址: localhost:27017"
    echo "  - 用户名: admin"
    echo "  - 密码: password123"
    echo "  - 数据库: download_scheduler_dev"
    echo ""
    echo "Redis:"
    echo "  - 地址: localhost:6379"
    echo "  - 密码: password123"
    echo ""
    echo "RabbitMQ:"
    echo "  - AMQP 地址: localhost:5672"
    echo "  - 管理界面: http://localhost:15672"
    echo "  - 用户名: admin"
    echo "  - 密码: password123"
    echo ""
    echo "可选管理工具（使用 --tools 参数启动）:"
    echo "  - MongoDB Express: http://localhost:8081"
    echo "  - Redis Commander: http://localhost:8082"
    echo ""
}

# 启动管理工具
start_tools() {
    log_info "启动管理工具..."
    docker-compose --profile tools up -d mongo-express redis-commander
    log_success "管理工具启动完成"
}

# 停止服务
stop_services() {
    log_info "停止中间件服务..."
    docker-compose down
    log_success "服务已停止"
}

# 清理数据
clean_data() {
    log_warning "这将删除所有数据，确认继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理数据..."
        docker-compose down -v
        log_success "数据清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "Download Scheduler 中间件设置脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start         启动中间件服务（默认）"
    echo "  stop          停止中间件服务"
    echo "  restart       重启中间件服务"
    echo "  status        显示服务状态"
    echo "  logs          显示服务日志"
    echo "  clean         清理所有数据（谨慎使用）"
    echo "  --tools       同时启动管理工具"
    echo "  --help, -h    显示此帮助信息"
    echo ""
}

# 主函数
main() {
    local action="start"
    local start_tools_flag=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            start|stop|restart|status|logs|clean)
                action="$1"
                shift
                ;;
            --tools)
                start_tools_flag=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查依赖
    check_dependencies
    check_docker_service
    
    # 执行操作
    case $action in
        start)
            create_config_files
            start_services
            if [ "$start_tools_flag" = true ]; then
                start_tools
            fi
            show_service_info
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            create_config_files
            start_services
            if [ "$start_tools_flag" = true ]; then
                start_tools
            fi
            show_service_info
            ;;
        status)
            docker-compose ps
            ;;
        logs)
            docker-compose logs -f
            ;;
        clean)
            clean_data
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
