{"rabbit_version": "3.11.0", "rabbitmq_version": "3.11.0", "product_name": "RabbitMQ", "product_version": "3.11.0", "users": [{"name": "admin", "password_hash": "password123", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "admin", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "cluster_name", "value": "download-scheduler-dev"}], "policies": [], "queues": [{"name": "download_tasks", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 10000}}, {"name": "worker_heartbeat", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 60000}}], "exchanges": [{"name": "download_scheduler", "vhost": "/", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": [{"source": "download_scheduler", "vhost": "/", "destination": "download_tasks", "destination_type": "queue", "routing_key": "task.download", "arguments": {}}, {"source": "download_scheduler", "vhost": "/", "destination": "worker_heartbeat", "destination_type": "queue", "routing_key": "worker.heartbeat", "arguments": {}}]}