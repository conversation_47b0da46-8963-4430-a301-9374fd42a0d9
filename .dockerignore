# Git
.git
.gitignore

# Documentation
README.md
docs/
*.md

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
._*
Thumbs.db

# Logs
logs/
*.log

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js build files (keep source)
web/.next/
web/out/
web/build/

# Test files
*_test.go
*Test.js
test/
tests/
coverage/
*.test
test-data/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Environment files (should be mounted at runtime)
.env*

# Build artifacts from other platforms
*.exe
*.dll

# Docker files
Dockerfile
docker-compose*.yml
.dockerignore

# Scripts
scripts/

# CI/CD
.github/
.gitlab-ci.yml

# Development files
integration_test.go
TEST_REPORT.md 